//
//  AppViewModel.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import Foundation
import SwiftUI

/// 应用主ViewModel
@MainActor
@Observable
class AppViewModel {
    // MARK: - Properties
    var voiceClones: [VoiceClone] = []
    var stories: [Story] = []
    var recordings: [AudioRecording] = []
    var selectedLanguage: Language = Language.supportedLanguages[0]
    var isLoading: Bool = false
    var errorMessage: String?
    var showError: Bool = false
    
    // MARK: - Services (将在后续实现中注入)
    private var audioRecordingService: AudioRecordingServiceProtocol?
    private var voiceCloneService: VoiceCloneServiceProtocol?
    private var storyGenerationService: StoryGenerationServiceProtocol?
    private var textToSpeechService: TextToSpeechServiceProtocol?
    private var audioPlayerService: AudioPlayerServiceProtocol?
    private var dataStorageService: DataStorageServiceProtocol?
    
    // MARK: - Initialization
    init() {
        // 初始化时加载数据
        loadData()
    }
    
    // MARK: - Data Loading
    func loadData() {
        Task {
            await loadAllData()
        }
    }
    
    private func loadAllData() async {
        isLoading = true
        defer { isLoading = false }
        
        // 加载数据（暂时使用模拟数据）
        await loadMockData()
    }
    
    private func loadMockData() async {
        // 模拟数据，用于开发阶段
        voiceClones = []
        stories = []
        recordings = []
    }
    
    // MARK: - Error Handling
    func handleError(_ error: Error) {
        errorMessage = error.localizedDescription
        showError = true
    }
    
    func clearError() {
        errorMessage = nil
        showError = false
    }
    
    // MARK: - Voice Clone Management
    func addVoiceClone(_ voiceClone: VoiceClone) {
        voiceClones.append(voiceClone)
    }
    
    func removeVoiceClone(_ voiceClone: VoiceClone) {
        voiceClones.removeAll { $0.id == voiceClone.id }
    }
    
    func updateVoiceClone(_ voiceClone: VoiceClone) {
        if let index = voiceClones.firstIndex(where: { $0.id == voiceClone.id }) {
            voiceClones[index] = voiceClone
        }
    }
    
    // MARK: - Story Management
    func addStory(_ story: Story) {
        stories.append(story)
    }

    func deleteStory(_ story: Story) {
        stories.removeAll { $0.id == story.id }
    }

    func deleteVoiceClone(_ voiceClone: VoiceClone) {
        voiceClones.removeAll { $0.id == voiceClone.id }
    }
    
    func removeStory(_ story: Story) {
        stories.removeAll { $0.id == story.id }
    }
    
    func updateStory(_ story: Story) {
        if let index = stories.firstIndex(where: { $0.id == story.id }) {
            stories[index] = story
        }
    }
    
    // MARK: - Recording Management
    func addRecording(_ recording: AudioRecording) {
        recordings.append(recording)
    }
    
    func removeRecording(_ recording: AudioRecording) {
        recordings.removeAll { $0.id == recording.id }
    }
    
    func updateRecording(_ recording: AudioRecording) {
        if let index = recordings.firstIndex(where: { $0.id == recording.id }) {
            recordings[index] = recording
        }
    }
    
    // MARK: - Language Management
    func setSelectedLanguage(_ language: Language) {
        selectedLanguage = language
    }
    
    // MARK: - Computed Properties
    var hasVoiceClones: Bool {
        return !voiceClones.isEmpty
    }
    
    var hasStories: Bool {
        return !stories.isEmpty
    }
    
    var hasRecordings: Bool {
        return !recordings.isEmpty
    }
    
    var completedVoiceClones: [VoiceClone] {
        return voiceClones.filter { $0.status.isCompleted }
    }
    
    var completedStories: [Story] {
        return stories.filter { $0.status.isCompleted }
    }
}
