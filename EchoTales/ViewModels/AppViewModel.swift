//
//  AppViewModel.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import Foundation
import SwiftUI

/// 应用主ViewModel
@MainActor
@Observable
class AppViewModel {
    // MARK: - Properties
    var voiceClones: [VoiceClone] = []
    var stories: [Story] = []
    var recordings: [AudioRecording] = []
    var selectedLanguage: Language = Language.supportedLanguages[0]
    var selectedUserRole: String = ""
    var isLoading: Bool = false
    var isInitialized: Bool = false
    var errorMessage: String?
    var showError: Bool = false

    // MARK: - API Keys
    var minimaxApiKey: String = "" {
        didSet { UserDefaults.standard.set(minimaxApiKey, forKey: "minimax_api_key") }
    }

    var deepseekApiKey: String = "" {
        didSet { UserDefaults.standard.set(deepseekApiKey, forKey: "deepseek_api_key") }
    }

    var isMinimaxConfigured: Bool { !minimaxApiKey.isEmpty }
    var isDeepseekConfigured: Bool { !deepseekApiKey.isEmpty }
    
    // MARK: - Services (将在后续实现中注入)
    private var audioRecordingService: AudioRecordingServiceProtocol?
    private var voiceCloneService: VoiceCloneServiceProtocol?
    private var storyGenerationService: StoryGenerationServiceProtocol?
    private var textToSpeechService: TextToSpeechServiceProtocol?
    private var audioPlayerService: AudioPlayerServiceProtocol?
    private var dataStorageService: DataStorageServiceProtocol?
    
    // MARK: - Initialization
    init() {
        // 从UserDefaults加载保存的值
        self.minimaxApiKey = UserDefaults.standard.string(forKey: "minimax_api_key") ?? ""
        self.deepseekApiKey = UserDefaults.standard.string(forKey: "deepseek_api_key") ?? ""
        self.selectedUserRole = UserDefaults.standard.string(forKey: "selected_user_role") ?? ""
    }

    func initialize() async {
        guard !isInitialized else { return }

        isLoading = true
        defer {
            isLoading = false
            isInitialized = true
        }

        // 加载数据（暂时使用模拟数据）
        await loadMockData()
    }
    
    // MARK: - Data Loading
    func loadData() {
        Task {
            await loadAllData()
        }
    }
    
    private func loadAllData() async {
        isLoading = true
        defer { isLoading = false }
        
        // 加载数据（暂时使用模拟数据）
        await loadMockData()
    }
    
    private func loadMockData() async {
        // 模拟数据，用于开发阶段
        voiceClones = []
        stories = []
        recordings = []
    }
    
    // MARK: - Error Handling
    func handleError(_ error: Error) {
        errorMessage = error.localizedDescription
        showError = true
    }
    
    func clearError() {
        errorMessage = nil
        showError = false
    }
    
    // MARK: - Voice Clone Management
    func addVoiceClone(_ voiceClone: VoiceClone) {
        voiceClones.append(voiceClone)
    }
    
    func removeVoiceClone(_ voiceClone: VoiceClone) {
        voiceClones.removeAll { $0.id == voiceClone.id }
    }
    
    func updateVoiceClone(_ voiceClone: VoiceClone) {
        if let index = voiceClones.firstIndex(where: { $0.id == voiceClone.id }) {
            voiceClones[index] = voiceClone
        }
    }
    
    // MARK: - Story Management
    func addStory(_ story: Story) {
        stories.append(story)
    }

    func deleteStory(_ story: Story) {
        stories.removeAll { $0.id == story.id }
    }

    func deleteVoiceClone(_ voiceClone: VoiceClone) {
        voiceClones.removeAll { $0.id == voiceClone.id }
    }
    
    func removeStory(_ story: Story) {
        stories.removeAll { $0.id == story.id }
    }
    
    func updateStory(_ story: Story) {
        if let index = stories.firstIndex(where: { $0.id == story.id }) {
            stories[index] = story
        }
    }
    
    // MARK: - Recording Management
    func addRecording(_ recording: AudioRecording) {
        recordings.append(recording)
    }
    
    func removeRecording(_ recording: AudioRecording) {
        recordings.removeAll { $0.id == recording.id }
    }
    
    func updateRecording(_ recording: AudioRecording) {
        if let index = recordings.firstIndex(where: { $0.id == recording.id }) {
            recordings[index] = recording
        }
    }
    
    // MARK: - Language Management
    func setSelectedLanguage(_ language: Language) {
        selectedLanguage = language
    }
    
    // MARK: - Computed Properties
    var hasVoiceClones: Bool {
        return !voiceClones.isEmpty
    }
    
    var hasStories: Bool {
        return !stories.isEmpty
    }
    
    var hasRecordings: Bool {
        return !recordings.isEmpty
    }
    
    var completedVoiceClones: [VoiceClone] {
        return voiceClones.filter { $0.status.isCompleted }
    }
    
    var completedStories: [Story] {
        return stories.filter { $0.status.isCompleted }
    }

    // MARK: - API Testing
    func testMinimaxConnection() async -> Bool {
        guard !minimaxApiKey.isEmpty else {
            print("MiniMax API Key为空")
            return false
        }

        // 简单的API Key格式验证
        if minimaxApiKey.count < 10 {
            print("MiniMax API Key格式不正确")
            return false
        }

        // 这里应该调用MiniMax API进行连接测试
        // 暂时进行基本验证，实际实现时需要真正的API调用
        do {
            // 模拟API调用延迟
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
            print("MiniMax API Key验证通过")
            return true
        } catch {
            print("MiniMax API连接测试失败: \(error)")
            return false
        }
    }

    func testDeepseekConnection() async -> Bool {
        guard !deepseekApiKey.isEmpty else {
            print("DeepSeek API Key为空")
            return false
        }

        // 简单的API Key格式验证
        if deepseekApiKey.count < 10 {
            print("DeepSeek API Key格式不正确")
            return false
        }

        // 这里应该调用DeepSeek API进行连接测试
        // 暂时进行基本验证，实际实现时需要真正的API调用
        do {
            // 模拟API调用延迟
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
            print("DeepSeek API Key验证通过")
            return true
        } catch {
            print("DeepSeek API连接测试失败: \(error)")
            return false
        }
    }
}
