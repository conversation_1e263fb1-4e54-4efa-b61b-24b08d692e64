//
//  StoryGenerationViewModel.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import Foundation
import SwiftUI

/// 故事生成界面ViewModel
@MainActor
class StoryGenerationViewModel: ObservableObject {
    
    // MARK: - Properties
    private let storyGenerationService: StoryGenerationService
    private var appViewModel: AppViewModel
    
    // MARK: - State
    @Published var theme: String = ""
    @Published var selectedLanguage: Language = Language.supportedLanguages[0]
    @Published var isGenerating: Bool = false
    @Published var generationProgress: Double = 0.0
    @Published var currentStory: Story?
    @Published var errorMessage: String?
    @Published var showError: Bool = false
    @Published var showSuccess: Bool = false
    @Published var successMessage: String = ""
    
    // MARK: - Configuration
    @Published var configuration: StoryGenerationConfiguration = .default
    
    // MARK: - Suggested Themes
    private let suggestedThemes: [String: [String]] = [
        "zh-CN": [
            "勇敢的小兔子", "魔法森林", "友谊的力量", "善良的公主",
            "聪明的小狐狸", "彩虹桥", "星星的愿望", "小熊的冒险",
            "神奇的种子", "月亮船", "会说话的动物", "梦想成真"
        ],
        "zh-HK": [
            "勇敢的小兔子", "魔法森林", "友誼的力量", "善良的公主",
            "聰明的小狐狸", "彩虹橋", "星星的願望", "小熊的冒險",
            "神奇的種子", "月亮船", "會說話的動物", "夢想成真"
        ],
        "en-US": [
            "Brave Little Rabbit", "Magic Forest", "Power of Friendship", "Kind Princess",
            "Clever Fox", "Rainbow Bridge", "Star's Wish", "Bear's Adventure",
            "Magic Seed", "Moon Boat", "Talking Animals", "Dreams Come True"
        ]
    ]
    
    // MARK: - Initialization
    init(storyGenerationService: StoryGenerationService, appViewModel: AppViewModel? = nil) {
        self.storyGenerationService = storyGenerationService
        self.appViewModel = appViewModel ?? AppViewModel()
        self.selectedLanguage = self.appViewModel.selectedLanguage
    }

    convenience init(storyGenerationService: StoryGenerationService) {
        self.init(storyGenerationService: storyGenerationService, appViewModel: nil)
    }

    func setAppViewModel(_ appViewModel: AppViewModel) {
        self.appViewModel = appViewModel
        self.selectedLanguage = appViewModel.selectedLanguage
    }
    
    // MARK: - Story Generation
    func generateStory() async {
        guard !theme.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            showError("请输入故事主题")
            return
        }
        
        isGenerating = true
        generationProgress = 0.0
        
        do {
            // 生成故事
            let story = try await storyGenerationService.generateStory(
                theme: theme.trimmingCharacters(in: .whitespacesAndNewlines),
                language: selectedLanguage
            )
            
            currentStory = story
            appViewModel.addStory(story)
            
            // 监控生成进度
            await monitorGenerationProgress(story)
            
            if story.status.isCompleted {
                showSuccess("故事生成成功！")
                // 自动开始语音合成
                await synthesizeSpeech(for: story)
            }
            
        } catch {
            showError("故事生成失败: \(error.localizedDescription)")
        }
        
        isGenerating = false
    }
    
    func cancelGeneration() async {
        guard let story = currentStory else { return }
        
        do {
            try await storyGenerationService.cancelGeneration(story)
            isGenerating = false
            generationProgress = 0.0
            currentStory = nil
        } catch {
            showError("取消失败: \(error.localizedDescription)")
        }
    }
    
    // MARK: - Theme Management
    func selectSuggestedTheme(_ suggestedTheme: String) {
        theme = suggestedTheme
    }
    
    func clearTheme() {
        theme = ""
        currentStory = nil
        generationProgress = 0.0
    }
    
    // MARK: - Language Selection
    func selectLanguage(_ language: Language) {
        selectedLanguage = language
        appViewModel.setSelectedLanguage(language)
    }
    
    // MARK: - Configuration
    func setConfiguration(_ config: StoryGenerationConfiguration) {
        configuration = config
    }
    
    func useCreativeMode() {
        configuration = .creative
    }
    
    func useConservativeMode() {
        configuration = .conservative
    }
    
    func useDefaultMode() {
        configuration = .default
    }
    
    // MARK: - Story Management
    func saveStory() {
        guard let _ = currentStory else { return }

        // 故事已经自动保存到应用状态中
        // 这里可以添加额外的保存逻辑，比如保存到云端

        showSuccess("故事已保存")
    }
    
    func shareStory() -> String? {
        guard let story = currentStory else { return nil }
        
        return """
        \(story.title)
        
        \(story.content)
        
        ——来自 EchoTales
        """
    }
    
    func regenerateStory() async {
        await generateStory()
    }

    // MARK: - Speech Synthesis

    /// 为故事合成语音
    private func synthesizeSpeech(for story: Story) async {
        // 检查是否有可用的声音克隆
        guard !appViewModel.voiceClones.isEmpty else {
            print("没有可用的声音克隆，跳过语音合成")
            return
        }

        // 检查MiniMax API配置
        guard !appViewModel.minimaxApiKey.isEmpty else {
            print("MiniMax API Key未配置，跳过语音合成")
            return
        }

        do {
            // 使用第一个可用的声音克隆
            let voiceClone = appViewModel.voiceClones.first!

            // 调用语音合成服务
            let textToSpeechService = TextToSpeechService()
            let audioURL = try await textToSpeechService.synthesizeSpeech(
                text: story.content,
                voiceClone: voiceClone
            )

            // 更新故事的音频URL
            story.setAudioUrl(audioURL)
            print("故事语音合成完成，音频文件: \(audioURL)")
            print("故事语音合成完成: \(audioURL)")

        } catch {
            print("语音合成失败: \(error.localizedDescription)")
            // 不显示错误给用户，因为这是自动操作
        }
    }
    
    // MARK: - Private Methods
    
    private func monitorGenerationProgress(_ story: Story) async {
        let maxAttempts = 30 // 最多监控30次（每秒检查一次）
        var attempts = 0
        
        while attempts < maxAttempts && story.status == .generating {
            try? await Task.sleep(nanoseconds: 1_000_000_000) // 等待1秒
            
            do {
                let progress = try await storyGenerationService.getGenerationProgress(story)
                generationProgress = progress
                
                if story.status.isCompleted {
                    generationProgress = 1.0
                    break
                } else if story.status.isFailed {
                    showError("故事生成失败")
                    break
                }
                
            } catch {
                showError("进度检查失败: \(error.localizedDescription)")
                break
            }
            
            attempts += 1
        }
        
        if attempts >= maxAttempts && story.status == .generating {
            story.updateStatus(.failed, errorMessage: "生成超时")
            showError("故事生成超时，请重试")
        }
    }
    
    // MARK: - Error Handling
    private func showError(_ message: String) {
        errorMessage = message
        showError = true
    }
    
    private func showSuccess(_ message: String) {
        successMessage = message
        showSuccess = true
    }
    
    func clearError() {
        errorMessage = nil
        showError = false
    }
    
    func clearSuccess() {
        successMessage = ""
        showSuccess = false
    }
    
    // MARK: - Computed Properties
    var canGenerate: Bool {
        return !theme.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty && !isGenerating
    }
    
    var currentSuggestedThemes: [String] {
        return suggestedThemes[selectedLanguage.code] ?? suggestedThemes["en-US"] ?? []
    }
    
    var hasStory: Bool {
        return currentStory != nil && currentStory?.status.isCompleted == true
    }
    
    var formattedProgress: String {
        return String(format: "%.0f%%", generationProgress * 100)
    }
    
    var estimatedTimeRemaining: String {
        if generationProgress > 0 {
            let totalTime: Double = 60 // 估计总时间1分钟
            let remainingTime = totalTime * (1 - generationProgress)
            let seconds = Int(remainingTime)
            return "\(seconds)秒"
        }
        return "计算中..."
    }
    
    var themeCharacterCount: Int {
        return theme.count
    }
    
    var isThemeValid: Bool {
        let trimmed = theme.trimmingCharacters(in: .whitespacesAndNewlines)
        return trimmed.count >= 2 && trimmed.count <= 100
    }
    
    var storyWordCount: Int {
        return currentStory?.wordCount ?? 0
    }
    
    var estimatedReadingTime: Int {
        return currentStory?.estimatedReadingTime ?? 0
    }
}
