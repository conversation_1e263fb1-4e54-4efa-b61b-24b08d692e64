//
//  StoryGenerationViewModel.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import Foundation
import SwiftUI

/// 故事生成界面ViewModel
@MainActor
class StoryGenerationViewModel: ObservableObject {
    
    // MARK: - Properties
    private let storyGenerationService: StoryGenerationService
    private let appViewModel: AppViewModel
    
    // MARK: - State
    var theme: String = ""
    var selectedLanguage: Language = Language.supportedLanguages[0]
    var isGenerating: Bool = false
    var generationProgress: Double = 0.0
    var currentStory: Story?
    var errorMessage: String?
    var showError: Bool = false
    var showSuccess: Bool = false
    var successMessage: String = ""
    
    // MARK: - Configuration
    var configuration: StoryGenerationConfiguration = .default
    
    // MARK: - Suggested Themes
    private let suggestedThemes: [String: [String]] = [
        "zh-CN": [
            "勇敢的小兔子", "魔法森林", "友谊的力量", "善良的公主",
            "聪明的小狐狸", "彩虹桥", "星星的愿望", "小熊的冒险",
            "神奇的种子", "月亮船", "会说话的动物", "梦想成真"
        ],
        "zh-HK": [
            "勇敢的小兔子", "魔法森林", "友誼的力量", "善良的公主",
            "聰明的小狐狸", "彩虹橋", "星星的願望", "小熊的冒險",
            "神奇的種子", "月亮船", "會說話的動物", "夢想成真"
        ],
        "en-US": [
            "Brave Little Rabbit", "Magic Forest", "Power of Friendship", "Kind Princess",
            "Clever Fox", "Rainbow Bridge", "Star's Wish", "Bear's Adventure",
            "Magic Seed", "Moon Boat", "Talking Animals", "Dreams Come True"
        ]
    ]
    
    // MARK: - Initialization
    init(storyGenerationService: StoryGenerationService, appViewModel: AppViewModel) {
        self.storyGenerationService = storyGenerationService
        self.appViewModel = appViewModel
        self.selectedLanguage = appViewModel.selectedLanguage
    }
    
    // MARK: - Story Generation
    func generateStory() async {
        guard !theme.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            showError("请输入故事主题")
            return
        }
        
        isGenerating = true
        generationProgress = 0.0
        
        do {
            // 生成故事
            let story = try await storyGenerationService.generateStory(
                theme: theme.trimmingCharacters(in: .whitespacesAndNewlines),
                language: selectedLanguage
            )
            
            currentStory = story
            appViewModel.addStory(story)
            
            // 监控生成进度
            await monitorGenerationProgress(story)
            
            if story.status.isCompleted {
                showSuccess("故事生成成功！")
            }
            
        } catch {
            showError("故事生成失败: \(error.localizedDescription)")
        }
        
        isGenerating = false
    }
    
    func cancelGeneration() async {
        guard let story = currentStory else { return }
        
        do {
            try await storyGenerationService.cancelGeneration(story)
            isGenerating = false
            generationProgress = 0.0
            currentStory = nil
        } catch {
            showError("取消失败: \(error.localizedDescription)")
        }
    }
    
    // MARK: - Theme Management
    func selectSuggestedTheme(_ suggestedTheme: String) {
        theme = suggestedTheme
    }
    
    func clearTheme() {
        theme = ""
        currentStory = nil
        generationProgress = 0.0
    }
    
    // MARK: - Language Selection
    func selectLanguage(_ language: Language) {
        selectedLanguage = language
        appViewModel.setSelectedLanguage(language)
    }
    
    // MARK: - Configuration
    func setConfiguration(_ config: StoryGenerationConfiguration) {
        configuration = config
    }
    
    func useCreativeMode() {
        configuration = .creative
    }
    
    func useConservativeMode() {
        configuration = .conservative
    }
    
    func useDefaultMode() {
        configuration = .default
    }
    
    // MARK: - Story Management
    func saveStory() {
        guard let _ = currentStory else { return }

        // 故事已经自动保存到应用状态中
        // 这里可以添加额外的保存逻辑，比如保存到云端

        showSuccess("故事已保存")
    }
    
    func shareStory() -> String? {
        guard let story = currentStory else { return nil }
        
        return """
        \(story.title)
        
        \(story.content)
        
        ——来自 EchoTales
        """
    }
    
    func regenerateStory() async {
        await generateStory()
    }
    
    // MARK: - Private Methods
    
    private func monitorGenerationProgress(_ story: Story) async {
        let maxAttempts = 30 // 最多监控30次（每秒检查一次）
        var attempts = 0
        
        while attempts < maxAttempts && story.status == .generating {
            try? await Task.sleep(nanoseconds: 1_000_000_000) // 等待1秒
            
            do {
                let progress = try await storyGenerationService.getGenerationProgress(story)
                generationProgress = progress
                
                if story.status.isCompleted {
                    generationProgress = 1.0
                    break
                } else if story.status.isFailed {
                    showError("故事生成失败")
                    break
                }
                
            } catch {
                showError("进度检查失败: \(error.localizedDescription)")
                break
            }
            
            attempts += 1
        }
        
        if attempts >= maxAttempts && story.status == .generating {
            story.updateStatus(.failed, errorMessage: "生成超时")
            showError("故事生成超时，请重试")
        }
    }
    
    // MARK: - Error Handling
    private func showError(_ message: String) {
        errorMessage = message
        showError = true
    }
    
    private func showSuccess(_ message: String) {
        successMessage = message
        showSuccess = true
    }
    
    func clearError() {
        errorMessage = nil
        showError = false
    }
    
    func clearSuccess() {
        successMessage = ""
        showSuccess = false
    }
    
    // MARK: - Computed Properties
    var canGenerate: Bool {
        return !theme.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty && !isGenerating
    }
    
    var currentSuggestedThemes: [String] {
        return suggestedThemes[selectedLanguage.code] ?? suggestedThemes["en-US"] ?? []
    }
    
    var hasStory: Bool {
        return currentStory != nil && currentStory?.status.isCompleted == true
    }
    
    var formattedProgress: String {
        return String(format: "%.0f%%", generationProgress * 100)
    }
    
    var estimatedTimeRemaining: String {
        if generationProgress > 0 {
            let totalTime: Double = 60 // 估计总时间1分钟
            let remainingTime = totalTime * (1 - generationProgress)
            let seconds = Int(remainingTime)
            return "\(seconds)秒"
        }
        return "计算中..."
    }
    
    var themeCharacterCount: Int {
        return theme.count
    }
    
    var isThemeValid: Bool {
        let trimmed = theme.trimmingCharacters(in: .whitespacesAndNewlines)
        return trimmed.count >= 2 && trimmed.count <= 100
    }
    
    var storyWordCount: Int {
        return currentStory?.wordCount ?? 0
    }
    
    var estimatedReadingTime: Int {
        return currentStory?.estimatedReadingTime ?? 0
    }
}
