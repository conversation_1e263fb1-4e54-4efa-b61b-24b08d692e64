//
//  RecordingViewModel.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import Foundation
import SwiftUI

/// 录音界面ViewModel
@MainActor
class RecordingViewModel: ObservableObject {
    
    // MARK: - Properties
    private let audioService: AudioRecordingService
    private let appViewModel: AppViewModel
    
    // MARK: - State
    var isRecording: Bool = false
    var isPlaying: Bool = false
    var currentRecording: AudioRecording?
    var selectedLanguage: Language = Language.supportedLanguages[0]
    var recordingDuration: TimeInterval = 0
    var audioLevel: Float = 0.0
    var isLoading: Bool = false
    var errorMessage: String?
    var showError: Bool = false
    var hasPermission: Bool = false
    var showPermissionAlert: Bool = false
    var isCreatingVoiceClone: Bool = false
    var voiceCloneCreated: Bool = false
    
    // MARK: - Timer
    private var recordingTimer: Timer?
    private var audioLevelTimer: Timer?
    
    // MARK: - Initialization
    init(audioService: AudioRecordingService, appViewModel: AppViewModel) {
        self.audioService = audioService
        self.appViewModel = appViewModel
        self.selectedLanguage = appViewModel.selectedLanguage
    }
    
    // MARK: - Permission Management
    func requestMicrophonePermission() async {
        isLoading = true
        defer { isLoading = false }
        
        hasPermission = await audioService.requestMicrophonePermission()
        
        if !hasPermission {
            showError("需要麦克风权限才能录音，请在设置中允许访问麦克风")
        }
    }
    
    // MARK: - Recording Control
    func startRecording() async {
        guard hasPermission else {
            await requestMicrophonePermission()
            return
        }
        
        isLoading = true
        
        do {
            currentRecording = try await audioService.startRecording(language: selectedLanguage)
            isRecording = true
            recordingDuration = 0
            
            startRecordingTimer()
            startAudioLevelTimer()
            
        } catch {
            showError("录音启动失败: \(error.localizedDescription)")
        }
        
        isLoading = false
    }
    
    func stopRecording() async {
        guard isRecording else { return }
        
        isLoading = true
        
        do {
            try await audioService.stopRecording()
            isRecording = false
            
            stopRecordingTimer()
            stopAudioLevelTimer()
            
            // 添加录音到应用状态
            if let recording = currentRecording {
                appViewModel.addRecording(recording)
            }
            
        } catch {
            showError("录音停止失败: \(error.localizedDescription)")
        }
        
        isLoading = false
    }
    
    func toggleRecording() async {
        if isRecording {
            await stopRecording()
        } else {
            await startRecording()
        }
    }
    
    // MARK: - Playback Control
    func playRecording() async {
        guard let recording = currentRecording else { return }
        
        isLoading = true
        
        do {
            try await audioService.playRecording(recording)
            isPlaying = true
        } catch {
            showError("播放失败: \(error.localizedDescription)")
        }
        
        isLoading = false
    }
    
    func stopPlaying() {
        audioService.stopPlaying()
        isPlaying = false
    }
    
    func togglePlayback() async {
        if isPlaying {
            stopPlaying()
        } else {
            await playRecording()
        }
    }
    
    // MARK: - Recording Management
    func deleteCurrentRecording() async {
        guard let recording = currentRecording else { return }
        
        isLoading = true
        
        do {
            try await audioService.deleteRecording(recording)
            appViewModel.removeRecording(recording)
            currentRecording = nil
            recordingDuration = 0
        } catch {
            showError("删除录音失败: \(error.localizedDescription)")
        }
        
        isLoading = false
    }
    
    func saveRecording() {
        guard currentRecording != nil else { return }

        // 录音已经自动保存到应用状态中
        // 这里可以添加额外的保存逻辑，比如保存到云端

        // 重置状态
        currentRecording = nil
        recordingDuration = 0
        audioLevel = 0.0
    }
    
    // MARK: - Language Selection
    func selectLanguage(_ language: Language) {
        selectedLanguage = language
        appViewModel.setSelectedLanguage(language)
    }
    
    // MARK: - Timer Management
    private func startRecordingTimer() {
        recordingTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateRecordingDuration()
            }
        }
    }
    
    private func stopRecordingTimer() {
        recordingTimer?.invalidate()
        recordingTimer = nil
    }
    
    private func startAudioLevelTimer() {
        audioLevelTimer = Timer.scheduledTimer(withTimeInterval: 0.05, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateAudioLevel()
            }
        }
    }
    
    private func stopAudioLevelTimer() {
        audioLevelTimer?.invalidate()
        audioLevelTimer = nil
        audioLevel = 0.0
    }
    
    private func updateRecordingDuration() {
        recordingDuration = audioService.getRecordingDuration()
    }
    
    private func updateAudioLevel() {
        audioLevel = audioService.getAudioLevel()
    }
    
    // MARK: - Error Handling
    private func showError(_ message: String) {
        errorMessage = message
        showError = true
    }
    
    func clearError() {
        errorMessage = nil
        showError = false
    }
    
    // MARK: - Computed Properties
    var formattedDuration: String {
        let minutes = Int(recordingDuration) / 60
        let seconds = Int(recordingDuration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    var normalizedAudioLevel: Double {
        // 将音频电平从 dB 转换为 0-1 范围
        // AVAudioRecorder 返回的是负值，-160 dB 是最小值，0 dB 是最大值
        let minDB: Float = -60.0
        let maxDB: Float = 0.0
        
        let clampedLevel = max(minDB, min(maxDB, audioLevel))
        return Double((clampedLevel - minDB) / (maxDB - minDB))
    }
    
    var canRecord: Bool {
        return hasPermission && !isLoading
    }
    
    var canPlay: Bool {
        return currentRecording != nil && !isLoading && !isRecording
    }
    
    var hasRecording: Bool {
        return currentRecording != nil
    }

    /// 创建语音克隆
    func createVoiceClone() async {
        guard let recording = currentRecording else { return }

        isCreatingVoiceClone = true

        do {
            let voiceClone = VoiceClone(
                name: "我的声音克隆",
                language: selectedLanguage,
                audioRecording: recording
            )

            // 这里应该调用语音克隆服务
            // await voiceCloneService.createVoiceClone(voiceClone)

            // 添加到应用数据
            appViewModel.addVoiceClone(voiceClone)
            voiceCloneCreated = true

        } catch {
            errorMessage = "创建语音克隆失败: \(error.localizedDescription)"
            showError = true
        }

        isCreatingVoiceClone = false
    }

    /// 删除录音
    func deleteRecording() {
        currentRecording = nil
        voiceCloneCreated = false
    }

    // MARK: - Cleanup
    deinit {
        // Clean up timers synchronously to avoid capturing self in Task
        recordingTimer?.invalidate()
        recordingTimer = nil
        audioLevelTimer?.invalidate()
        audioLevelTimer = nil
    }
}
