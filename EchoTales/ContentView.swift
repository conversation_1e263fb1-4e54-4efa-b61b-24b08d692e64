//
//  ContentView.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import SwiftUI

struct ContentView: View {
    @Environment(AppViewModel.self) private var appViewModel

    var body: some View {
        Group {
            if appViewModel.isInitialized {
                MainView()
            } else {
                LoadingView()
            }
        }
    }
}

/// 加载视图
struct LoadingView: View {
    @State private var showCover = true

    var body: some View {
        ZStack {
            if showCover {
                // 封面图
                Image("OpenImage")
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .ignoresSafeArea()
                    .onAppear {
                        // 显示封面图2秒后开始加载动画
                        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                            withAnimation(.easeInOut(duration: 0.5)) {
                                showCover = false
                            }
                        }
                    }
            } else {
                // 加载界面
                VStack(spacing: 20) {
                    Image(systemName: "waveform.and.mic")
                        .font(.system(size: 60))
                        .foregroundStyle(.blue.gradient)

                    Text("EchoTales")
                        .font(.largeTitle)
                        .fontWeight(.bold)

                    Text("正在初始化...")
                        .font(.subheadline)
                        .foregroundStyle(.secondary)

                    ProgressView()
                        .scaleEffect(1.2)
                        .padding(.top)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(.ultraThinMaterial)
            }
        }
    }
}

#Preview {
    ContentView()
        .environment(AppViewModel())
}
