//
//  ContentView.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import SwiftUI

struct ContentView: View {
    @Environment(AppViewModel.self) private var appViewModel

    var body: some View {
        Group {
            if appViewModel.isInitialized {
                MainView()
            } else {
                LoadingView()
            }
        }
    }
}

/// 加载视图
struct LoadingView: View {
    @State private var showCover = true
    @State private var permissionsRequested = false

    var body: some View {
        ZStack {
            if showCover {
                // 封面图
                Image("OpenImage")
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .ignoresSafeArea()
                    .onAppear {
                        // 显示封面图3秒后开始权限申请和加载
                        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                            requestPermissions()
                        }
                    }
            } else {
                // 加载界面
                VStack(spacing: 20) {
                    Image(systemName: "waveform.and.mic")
                        .font(.system(size: 60))
                        .foregroundStyle(.blue.gradient)

                    Text("EchoTales")
                        .font(.largeTitle)
                        .fontWeight(.bold)

                    Text("正在初始化...")
                        .font(.subheadline)
                        .foregroundStyle(.secondary)

                    ProgressView()
                        .scaleEffect(1.2)
                        .padding(.top)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(.ultraThinMaterial)
            }
        }
    }

    private func requestPermissions() {
        Task {
            // 首先请求网络权限（通过尝试网络连接来触发）
            await requestNetworkPermission()

            // 然后请求录音权限
            await requestMicrophonePermission()

            // 权限申请完成后，隐藏封面并开始加载
            await MainActor.run {
                withAnimation(.easeInOut(duration: 0.5)) {
                    showCover = false
                }
            }
        }
    }

    private func requestNetworkPermission() async {
        // 通过尝试网络请求来触发网络权限申请
        do {
            let url = URL(string: "https://www.apple.com")!
            let (_, _) = try await URLSession.shared.data(from: url)
            print("网络权限已获取")
        } catch {
            print("网络权限申请: \(error)")
        }
    }

    private func requestMicrophonePermission() async {
        // 请求麦克风权限
        let audioService = AudioRecordingService()
        let hasPermission = await audioService.requestMicrophonePermission()
        print("麦克风权限状态: \(hasPermission)")
    }
}

#Preview {
    ContentView()
        .environment(AppViewModel())
}
