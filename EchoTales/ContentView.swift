//
//  ContentView.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import SwiftUI

struct ContentView: View {
    var body: some View {
        NavigationStack {
            VStack {
                Image(systemName: "waveform.and.mic")
                    .imageScale(.large)
                    .foregroundStyle(.tint)
                Text("EchoTales")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                Text("正在构建中...")
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }
            .padding()
            .navigationTitle("EchoTales")
        }
    }
}

#Preview {
    ContentView()
}
