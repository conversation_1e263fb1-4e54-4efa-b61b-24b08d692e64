//
//  EchoTalesApp.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import SwiftUI

@main
struct EchoTalesApp: App {
    @State private var appViewModel = AppViewModel()

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(appViewModel)
                .task {
                    await appViewModel.initialize()
                }
        }
    }
}
