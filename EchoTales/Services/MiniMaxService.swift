//
//  MiniMaxService.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import Foundation

/// MiniMax API响应基础结构
struct MiniMaxBaseResponse: Codable {
    let statusCode: Int
    let statusMsg: String
    
    enum CodingKeys: String, CodingKey {
        case statusCode = "status_code"
        case statusMsg = "status_msg"
    }
}

/// 文件上传响应
struct FileUploadResponse: Codable {
    let file: FileInfo
    let baseResp: MiniMaxBaseResponse
    
    enum CodingKeys: String, CodingKey {
        case file
        case baseResp = "base_resp"
    }
}

struct FileInfo: Codable {
    let fileId: String
    let fileName: String
    let purpose: String
    
    enum CodingKeys: String, CodingKey {
        case fileId = "file_id"
        case fileName = "filename"
        case purpose
    }
}

/// 语音克隆请求
struct VoiceCloneRequest: Codable {
    let fileId: String
    let voiceId: String
    let noiseReduction: Bool?
    let text: String?
    let model: String?
    let accuracy: Double?
    let needVolumeNormalization: Bool?
    
    enum CodingKeys: String, CodingKey {
        case fileId = "file_id"
        case voiceId = "voice_id"
        case noiseReduction = "noise_reduction"
        case text
        case model
        case accuracy
        case needVolumeNormalization = "need_volume_normalization"
    }
}

/// 语音克隆响应
struct VoiceCloneResponse: Codable {
    let inputSensitive: Bool
    let inputSensitiveType: Int
    let baseResp: MiniMaxBaseResponse
    
    enum CodingKeys: String, CodingKey {
        case inputSensitive = "input_sensitive"
        case inputSensitiveType = "input_sensitive_type"
        case baseResp = "base_resp"
    }
}

/// 文本转语音请求
struct TextToSpeechRequest: Codable {
    let model: String
    let text: String
    let voiceId: String
    let responseFormat: String?
    let speed: Double?
    let volume: Double?
    
    enum CodingKeys: String, CodingKey {
        case model
        case text
        case voiceId = "voice_id"
        case responseFormat = "response_format"
        case speed
        case volume
    }
}

/// MiniMax API服务
@MainActor
class MiniMaxService {
    private let baseURL = "https://api.minimaxi.chat/v1"
    private let networkService = NetworkService.shared
    
    // API配置 - 在实际应用中应该从安全存储中获取
    private var groupId: String {
        // TODO: 从配置或环境变量中获取
        return "your_group_id"
    }
    
    private var apiKey: String {
        // TODO: 从安全存储中获取
        return "your_api_key"
    }
    
    /// 上传音频文件用于语音克隆
    func uploadAudioFile(audioData: Data, fileName: String) async throws -> String {
        guard let url = URL(string: "\(baseURL)/files/upload?GroupId=\(groupId)") else {
            throw NetworkError.invalidURL
        }
        
        let _ = [
            "Authorization": "Bearer \(apiKey)"
        ]
        
        // 创建multipart/form-data请求体
        let boundary = UUID().uuidString
        var body = Data()
        
        // 添加purpose字段
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"purpose\"\r\n\r\n".data(using: .utf8)!)
        body.append("voice_clone\r\n".data(using: .utf8)!)
        
        // 添加文件数据
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"file\"; filename=\"\(fileName)\"\r\n".data(using: .utf8)!)
        body.append("Content-Type: audio/mpeg\r\n\r\n".data(using: .utf8)!)
        body.append(audioData)
        body.append("\r\n".data(using: .utf8)!)
        body.append("--\(boundary)--\r\n".data(using: .utf8)!)
        
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")
        urlRequest.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        urlRequest.httpBody = body
        
        do {
            let (data, response) = try await URLSession.shared.data(for: urlRequest)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw NetworkError.invalidResponse
            }
            
            guard 200...299 ~= httpResponse.statusCode else {
                let errorMessage = String(data: data, encoding: .utf8)
                throw NetworkError.httpError(httpResponse.statusCode, errorMessage)
            }
            
            let uploadResponse = try JSONDecoder().decode(FileUploadResponse.self, from: data)
            
            if uploadResponse.baseResp.statusCode != 0 {
                throw NetworkError.serverError(uploadResponse.baseResp.statusMsg)
            }
            
            return uploadResponse.file.fileId
            
        } catch let error as NetworkError {
            throw error
        } catch {
            throw NetworkError.networkError(error)
        }
    }
    
    /// 创建语音克隆
    func createVoiceClone(fileId: String, voiceId: String, noiseReduction: Bool = false) async throws -> Bool {
        guard let url = URL(string: "\(baseURL)/voice_clone?GroupId=\(groupId)") else {
            throw NetworkError.invalidURL
        }
        
        let request = VoiceCloneRequest(
            fileId: fileId,
            voiceId: voiceId,
            noiseReduction: noiseReduction,
            text: nil,
            model: "speech-01-turbo",
            accuracy: 0.7,
            needVolumeNormalization: false
        )
        
        let headers = [
            "Authorization": "Bearer \(apiKey)",
            "Content-Type": "application/json"
        ]
        
        let body = try networkService.encodeBody(request)
        let networkRequest = NetworkRequest(url: url, method: .POST, headers: headers, body: body)
        
        let response: VoiceCloneResponse = try await networkService.performRequest(networkRequest, responseType: VoiceCloneResponse.self)
        
        if response.baseResp.statusCode != 0 {
            throw NetworkError.serverError(response.baseResp.statusMsg)
        }
        
        return true
    }
    
    /// 使用克隆的声音进行文本转语音
    func synthesizeSpeech(text: String, voiceId: String) async throws -> Data {
        guard let url = URL(string: "\(baseURL)/t2a_v2?GroupId=\(groupId)") else {
            throw NetworkError.invalidURL
        }
        
        let request = TextToSpeechRequest(
            model: "speech-01-turbo",
            text: text,
            voiceId: voiceId,
            responseFormat: "mp3",
            speed: 1.0,
            volume: 1.0
        )
        
        let _ = [
            "Authorization": "Bearer \(apiKey)",
            "Content-Type": "application/json"
        ]
        
        let body = try networkService.encodeBody(request)
        
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.httpBody = body
        
        do {
            let (data, response) = try await URLSession.shared.data(for: urlRequest)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw NetworkError.invalidResponse
            }
            
            guard 200...299 ~= httpResponse.statusCode else {
                let errorMessage = String(data: data, encoding: .utf8)
                throw NetworkError.httpError(httpResponse.statusCode, errorMessage)
            }
            
            return data
            
        } catch let error as NetworkError {
            throw error
        } catch {
            throw NetworkError.networkError(error)
        }
    }
}
