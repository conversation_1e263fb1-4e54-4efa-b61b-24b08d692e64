//
//  ServiceProtocols.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import Foundation
import AVFoundation

/// 录音服务协议
protocol AudioRecordingServiceProtocol {
    /// 请求麦克风权限
    func requestMicrophonePermission() async -> Bool
    
    /// 开始录音
    func startRecording(language: Language) async throws -> AudioRecording
    
    /// 停止录音
    func stopRecording() async throws
    
    /// 播放录音
    func playRecording(_ recording: AudioRecording) async throws
    
    /// 停止播放
    func stopPlaying()
    
    /// 删除录音文件
    func deleteRecording(_ recording: AudioRecording) async throws
    
    /// 获取录音时长
    func getRecordingDuration() -> TimeInterval
    
    /// 录音状态
    var isRecording: Bool { get }
    var isPlaying: Bool { get }
}

/// 语音克隆服务协议
protocol VoiceCloneServiceProtocol {
    /// 创建语音克隆
    func createVoiceClone(from recording: AudioRecording, name: String) async throws -> VoiceClone
    
    /// 查询克隆状态
    func checkCloneStatus(_ voiceClone: VoiceClone) async throws -> VoiceCloneStatus
    
    /// 获取所有语音克隆
    func getAllVoiceClones() async throws -> [VoiceClone]
    
    /// 删除语音克隆
    func deleteVoiceClone(_ voiceClone: VoiceClone) async throws
}

/// 故事生成服务协议
protocol StoryGenerationServiceProtocol {
    /// 生成故事
    func generateStory(theme: String, language: Language) async throws -> Story
    
    /// 获取生成进度
    func getGenerationProgress(_ story: Story) async throws -> Double
    
    /// 取消故事生成
    func cancelGeneration(_ story: Story) async throws
}

/// 文本转语音服务协议
protocol TextToSpeechServiceProtocol {
    /// 使用克隆声音合成语音
    func synthesizeSpeech(text: String, voiceClone: VoiceClone) async throws -> URL
    
    /// 获取合成进度
    func getSynthesisProgress() async -> Double
    
    /// 取消语音合成
    func cancelSynthesis() async throws
}

/// 音频播放服务协议
@MainActor
protocol AudioPlayerServiceProtocol {
    /// 播放音频
    func play(url: URL) async throws

    /// 暂停播放
    func pause()

    /// 恢复播放
    func resume()

    /// 停止播放
    func stop()

    /// 跳转到指定时间
    func seek(to time: TimeInterval)

    /// 获取当前播放时间
    var currentTime: TimeInterval { get }

    /// 获取音频总时长
    var duration: TimeInterval { get }

    /// 播放状态
    var isPlaying: Bool { get }
    var isPaused: Bool { get }
}

/// 数据存储服务协议
protocol DataStorageServiceProtocol {
    /// 保存语音克隆
    func saveVoiceClone(_ voiceClone: VoiceClone) async throws
    
    /// 加载所有语音克隆
    func loadVoiceClones() async throws -> [VoiceClone]
    
    /// 删除语音克隆
    func deleteVoiceClone(_ voiceClone: VoiceClone) async throws
    
    /// 保存故事
    func saveStory(_ story: Story) async throws
    
    /// 加载所有故事
    func loadStories() async throws -> [Story]
    
    /// 删除故事
    func deleteStory(_ story: Story) async throws
    
    /// 保存录音
    func saveRecording(_ recording: AudioRecording) async throws
    
    /// 加载所有录音
    func loadRecordings() async throws -> [AudioRecording]
    
    /// 删除录音
    func deleteRecording(_ recording: AudioRecording) async throws
}
