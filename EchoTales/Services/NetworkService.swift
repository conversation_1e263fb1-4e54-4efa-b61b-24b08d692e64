//
//  NetworkService.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import Foundation

/// 网络请求错误类型
enum NetworkError: Error, LocalizedError {
    case invalidURL
    case noData
    case decodingError(Error)
    case encodingError(Error)
    case httpError(Int, String?)
    case networkError(Error)
    case authenticationFailed
    case invalidResponse
    case authenticationError
    case rateLimitExceeded
    case serverError(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .noData:
            return "没有数据返回"
        case .decodingError(let error):
            return "数据解析错误: \(error.localizedDescription)"
        case .encodingError(let error):
            return "数据编码错误: \(error.localizedDescription)"
        case .httpError(let code, let message):
            return "HTTP错误 \(code): \(message ?? "未知错误")"
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        case .invalidResponse:
            return "无效的响应"
        case .authenticationError:
            return "认证失败，请检查API密钥"
        case .rateLimitExceeded:
            return "请求频率超限，请稍后重试"
        case .serverError(let message):
            return "服务器错误: \(message)"
        case .authenticationFailed:
            return "认证失败，请检查API密钥"
        }
    }
}

/// HTTP方法
enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PUT = "PUT"
    case DELETE = "DELETE"
    case PATCH = "PATCH"
}

/// 网络请求配置
struct NetworkRequest {
    let url: URL
    let method: HTTPMethod
    let headers: [String: String]
    let body: Data?
    let timeout: TimeInterval
    
    init(url: URL, method: HTTPMethod = .GET, headers: [String: String] = [:], body: Data? = nil, timeout: TimeInterval = 30.0) {
        self.url = url
        self.method = method
        self.headers = headers
        self.body = body
        self.timeout = timeout
    }
}

/// 网络服务基础类
@MainActor
class NetworkService {
    static let shared = NetworkService()
    
    private let session: URLSession
    private let decoder: JSONDecoder
    private let encoder: JSONEncoder
    
    private init() {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30.0
        config.timeoutIntervalForResource = 60.0
        self.session = URLSession(configuration: config)
        
        self.decoder = JSONDecoder()
        self.decoder.dateDecodingStrategy = .secondsSince1970
        
        self.encoder = JSONEncoder()
        self.encoder.dateEncodingStrategy = .secondsSince1970
    }
    
    /// 执行网络请求
    func performRequest<T: Codable>(_ request: NetworkRequest, responseType: T.Type) async throws -> T {
        var urlRequest = URLRequest(url: request.url)
        urlRequest.httpMethod = request.method.rawValue
        urlRequest.timeoutInterval = request.timeout
        
        // 设置请求头
        for (key, value) in request.headers {
            urlRequest.setValue(value, forHTTPHeaderField: key)
        }
        
        // 设置请求体
        if let body = request.body {
            urlRequest.httpBody = body
        }
        
        do {
            let (data, response) = try await session.data(for: urlRequest)
            
            // 检查HTTP响应状态
            guard let httpResponse = response as? HTTPURLResponse else {
                throw NetworkError.invalidResponse
            }
            
            // 处理HTTP错误状态码
            switch httpResponse.statusCode {
            case 200...299:
                break
            case 401:
                throw NetworkError.authenticationError
            case 429:
                throw NetworkError.rateLimitExceeded
            case 400...499:
                let errorMessage = String(data: data, encoding: .utf8)
                throw NetworkError.httpError(httpResponse.statusCode, errorMessage)
            case 500...599:
                let errorMessage = String(data: data, encoding: .utf8)
                throw NetworkError.serverError(errorMessage ?? "服务器内部错误")
            default:
                throw NetworkError.httpError(httpResponse.statusCode, nil)
            }
            
            // 解析响应数据
            do {
                let result = try decoder.decode(responseType, from: data)
                return result
            } catch {
                throw NetworkError.decodingError(error)
            }
            
        } catch let error as NetworkError {
            throw error
        } catch {
            throw NetworkError.networkError(error)
        }
    }
    
    /// 上传文件
    func uploadFile(to url: URL, fileData: Data, fileName: String, mimeType: String, headers: [String: String] = [:]) async throws -> Data {
        let boundary = UUID().uuidString
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")
        
        // 设置其他请求头
        for (key, value) in headers {
            urlRequest.setValue(value, forHTTPHeaderField: key)
        }
        
        // 构建multipart/form-data请求体
        var body = Data()
        
        // 添加文件数据
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"file\"; filename=\"\(fileName)\"\r\n".data(using: .utf8)!)
        body.append("Content-Type: \(mimeType)\r\n\r\n".data(using: .utf8)!)
        body.append(fileData)
        body.append("\r\n".data(using: .utf8)!)
        body.append("--\(boundary)--\r\n".data(using: .utf8)!)
        
        urlRequest.httpBody = body
        
        do {
            let (data, response) = try await session.data(for: urlRequest)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw NetworkError.invalidResponse
            }
            
            guard 200...299 ~= httpResponse.statusCode else {
                let errorMessage = String(data: data, encoding: .utf8)
                throw NetworkError.httpError(httpResponse.statusCode, errorMessage)
            }
            
            return data
        } catch let error as NetworkError {
            throw error
        } catch {
            throw NetworkError.networkError(error)
        }
    }
    
    /// 编码请求体
    func encodeBody<T: Codable>(_ object: T) throws -> Data {
        do {
            return try encoder.encode(object)
        } catch {
            throw NetworkError.encodingError(error)
        }
    }
}
