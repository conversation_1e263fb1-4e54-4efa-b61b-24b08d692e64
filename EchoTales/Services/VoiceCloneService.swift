//
//  VoiceCloneService.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import Foundation

/// 语音克隆服务实现
@MainActor
class VoiceCloneService: VoiceCloneServiceProtocol {
    
    // MARK: - Properties
    private let miniMaxService: MiniMaxService
    private let networkService: NetworkService
    
    // MARK: - Initialization
    init() {
        self.miniMaxService = MiniMaxService()
        self.networkService = NetworkService.shared
    }
    
    // MARK: - VoiceCloneServiceProtocol Implementation
    
    func createVoiceClone(from recording: AudioRecording, name: String) async throws -> VoiceClone {
        // 创建语音克隆对象
        let voiceClone = VoiceClone(name: name, language: recording.language, audioRecording: recording)
        
        // 更新状态为处理中
        voiceClone.updateStatus(.processing, progress: 0.1)
        
        do {
            // 1. 读取音频文件数据
            guard let audioURL = recording.filePath,
                  FileManager.default.fileExists(atPath: audioURL.path) else {
                throw NSError(domain: "VoiceCloneService", code: 1001, userInfo: [NSLocalizedDescriptionKey: "录音文件不存在"])
            }
            
            let audioData = try Data(contentsOf: audioURL)
            voiceClone.updateStatus(.processing, progress: 0.3)
            
            // 2. 上传音频文件到MiniMax
            let fileId = try await miniMaxService.uploadAudioFile(
                audioData: audioData,
                fileName: recording.fileName
            )
            voiceClone.updateStatus(.processing, progress: 0.6)
            
            // 3. 创建语音克隆
            let voiceId = generateVoiceId(for: name)
            let success = try await miniMaxService.createVoiceClone(
                fileId: fileId,
                voiceId: voiceId,
                noiseReduction: true
            )
            
            if success {
                voiceClone.setCloneId(voiceId)
                voiceClone.updateStatus(.completed, progress: 1.0)
            } else {
                voiceClone.updateStatus(.failed, errorMessage: "语音克隆创建失败")
            }
            
            return voiceClone
            
        } catch {
            voiceClone.updateStatus(.failed, errorMessage: error.localizedDescription)
            throw error
        }
    }
    
    func checkCloneStatus(_ voiceClone: VoiceClone) async throws -> VoiceCloneStatus {
        // 在实际实现中，这里应该调用MiniMax API查询克隆状态
        // 目前返回当前状态
        return voiceClone.status
    }
    
    func getAllVoiceClones() async throws -> [VoiceClone] {
        // 在实际实现中，这里应该从本地存储或服务器获取所有语音克隆
        // 目前返回空数组
        return []
    }
    
    func deleteVoiceClone(_ voiceClone: VoiceClone) async throws {
        // 在实际实现中，这里应该调用MiniMax API删除语音克隆
        // 并从本地存储中删除相关数据
        
        // 删除关联的录音文件
        try voiceClone.audioRecording.deleteFile()
    }
    
    // MARK: - Private Methods
    
    /// 生成语音ID
    private func generateVoiceId(for name: String) -> String {
        let timestamp = Int(Date().timeIntervalSince1970)
        let sanitizedName = name.replacingOccurrences(of: " ", with: "_")
            .replacingOccurrences(of: "[^a-zA-Z0-9_]", with: "", options: .regularExpression)
        return "voice_\(sanitizedName)_\(timestamp)"
    }
    
    /// 验证音频文件格式
    private func validateAudioFile(_ audioData: Data) throws {
        // 检查文件大小（限制为50MB）
        let maxSize = 50 * 1024 * 1024 // 50MB
        guard audioData.count <= maxSize else {
            throw NSError(domain: "VoiceCloneService", code: 1002, userInfo: [NSLocalizedDescriptionKey: "音频文件过大，请确保文件小于50MB"])
        }
        
        // 检查文件最小大小（至少1KB）
        let minSize = 1024 // 1KB
        guard audioData.count >= minSize else {
            throw NSError(domain: "VoiceCloneService", code: 1003, userInfo: [NSLocalizedDescriptionKey: "音频文件过小，请确保录音时长足够"])
        }
    }
    
    /// 估算处理时间
    private func estimateProcessingTime(for audioData: Data) -> TimeInterval {
        // 根据文件大小估算处理时间
        let sizeInMB = Double(audioData.count) / (1024 * 1024)
        return max(30, sizeInMB * 10) // 最少30秒，每MB大约10秒
    }
}

/// 语音克隆错误类型
enum VoiceCloneError: Error, LocalizedError {
    case invalidAudioFile
    case uploadFailed(String)
    case cloneCreationFailed(String)
    case cloneNotFound
    case networkError(Error)
    
    var errorDescription: String? {
        switch self {
        case .invalidAudioFile:
            return "无效的音频文件"
        case .uploadFailed(let message):
            return "上传失败: \(message)"
        case .cloneCreationFailed(let message):
            return "语音克隆创建失败: \(message)"
        case .cloneNotFound:
            return "未找到语音克隆"
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        }
    }
}

/// 语音克隆配置
struct VoiceCloneConfiguration {
    let noiseReduction: Bool
    let accuracy: Double
    let volumeNormalization: Bool
    
    static let `default` = VoiceCloneConfiguration(
        noiseReduction: true,
        accuracy: 0.7,
        volumeNormalization: false
    )
    
    static let highQuality = VoiceCloneConfiguration(
        noiseReduction: true,
        accuracy: 0.9,
        volumeNormalization: true
    )
}
