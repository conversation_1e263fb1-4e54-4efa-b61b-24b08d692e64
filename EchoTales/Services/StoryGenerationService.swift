//
//  StoryGenerationService.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import Foundation

/// 故事生成服务实现
@MainActor
class StoryGenerationService: StoryGenerationServiceProtocol {
    
    // MARK: - Properties
    private let deepSeekService: DeepSeekService
    private var currentGenerationTask: Task<Void, Never>?
    private var generationProgress: [UUID: Double] = [:]
    
    // MARK: - Initialization
    init() {
        self.deepSeekService = DeepSeekService()
    }
    
    // MARK: - StoryGenerationServiceProtocol Implementation
    
    func generateStory(theme: String, language: Language) async throws -> Story {
        // 创建故事对象
        let story = Story(theme: theme, language: language)
        
        // 更新状态为生成中
        story.updateStatus(.generating, progress: 0.1)
        generationProgress[story.id] = 0.1
        
        do {
            // 调用DeepSeek API生成故事
            let content = try await deepSeekService.generateStory(
                theme: theme,
                language: language,
                maxTokens: 2000
            )
            
            // 解析故事内容
            let (title, storyContent) = deepSeekService.parseStoryContent(content)
            
            // 设置故事内容
            story.setContent(storyContent, title: title.isEmpty ? generateDefaultTitle(theme: theme, language: language) : title)
            
            // 更新状态为完成
            story.updateStatus(.completed, progress: 1.0)
            generationProgress[story.id] = 1.0
            
            return story
            
        } catch {
            story.updateStatus(.failed, errorMessage: error.localizedDescription)
            generationProgress.removeValue(forKey: story.id)
            throw error
        }
    }
    
    func getGenerationProgress(_ story: Story) async throws -> Double {
        return generationProgress[story.id] ?? story.progress
    }
    
    func cancelGeneration(_ story: Story) async throws {
        currentGenerationTask?.cancel()
        currentGenerationTask = nil
        generationProgress.removeValue(forKey: story.id)
        story.updateStatus(.failed, errorMessage: "生成已取消")
    }
    
    // MARK: - Private Methods
    
    /// 生成默认标题
    private func generateDefaultTitle(theme: String, language: Language) -> String {
        switch language.code {
        case "zh-CN":
            return "\(theme)的故事"
        case "zh-HK":
            return "\(theme)的故事"
        case "en-US":
            return "The Story of \(theme)"
        default:
            return "The Story of \(theme)"
        }
    }
    
    /// 验证主题内容
    private func validateTheme(_ theme: String) throws {
        let trimmedTheme = theme.trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard !trimmedTheme.isEmpty else {
            throw StoryGenerationError.emptyTheme
        }
        
        guard trimmedTheme.count >= 2 else {
            throw StoryGenerationError.themeTooShort
        }
        
        guard trimmedTheme.count <= 100 else {
            throw StoryGenerationError.themeTooLong
        }
        
        // 检查是否包含不当内容
        let inappropriateWords = ["暴力", "恐怖", "血腥", "violence", "horror", "blood"]
        for word in inappropriateWords {
            if trimmedTheme.lowercased().contains(word.lowercased()) {
                throw StoryGenerationError.inappropriateContent
            }
        }
    }
    
    /// 生成故事提示词
    private func generatePrompt(theme: String, language: Language) -> String {
        let basePrompt = createBasePrompt(for: language)
        let themePrompt = createThemePrompt(theme: theme, language: language)
        return "\(basePrompt)\n\n\(themePrompt)"
    }
    
    private func createBasePrompt(for language: Language) -> String {
        switch language.code {
        case "zh-CN":
            return """
            你是一个专业的儿童故事作家。请创作一个温馨、有教育意义的童话故事。
            
            要求：
            1. 故事内容积极正面，适合3-12岁儿童
            2. 语言简洁易懂，富有想象力
            3. 包含明确的道德教育意义
            4. 故事长度500-800字
            5. 避免暴力、恐怖内容
            6. 结构完整：开头、发展、高潮、结尾
            """
        case "zh-HK":
            return """
            你是一個專業的兒童故事作家。請創作一個溫馨、有教育意義的童話故事。
            
            要求：
            1. 故事內容積極正面，適合3-12歲兒童
            2. 語言簡潔易懂，富有想像力
            3. 包含明確的道德教育意義
            4. 故事長度500-800字
            5. 避免暴力、恐怖內容
            6. 結構完整：開頭、發展、高潮、結尾
            """
        case "en-US":
            return """
            You are a professional children's story writer. Please create a warm and educational fairy tale.
            
            Requirements:
            1. Positive content suitable for children aged 3-12
            2. Simple, imaginative language
            3. Clear moral and educational value
            4. Story length 500-800 words
            5. Avoid violence and scary content
            6. Complete structure: beginning, development, climax, ending
            """
        default:
            return """
            You are a professional children's story writer. Please create a warm and educational fairy tale.
            
            Requirements:
            1. Positive content suitable for children aged 3-12
            2. Simple, imaginative language
            3. Clear moral and educational value
            4. Story length 500-800 words
            5. Avoid violence and scary content
            6. Complete structure: beginning, development, climax, ending
            """
        }
    }
    
    private func createThemePrompt(theme: String, language: Language) -> String {
        switch language.code {
        case "zh-CN":
            return "请以「\(theme)」为主题创作故事，包含一个吸引人的标题。"
        case "zh-HK":
            return "請以「\(theme)」為主題創作故事，包含一個吸引人的標題。"
        case "en-US":
            return "Please create a story with the theme '\(theme)', including an engaging title."
        default:
            return "Please create a story with the theme '\(theme)', including an engaging title."
        }
    }
}

/// 故事生成错误类型
enum StoryGenerationError: Error, LocalizedError {
    case emptyTheme
    case themeTooShort
    case themeTooLong
    case inappropriateContent
    case generationFailed(String)
    case networkError(Error)
    case rateLimitExceeded
    case serviceUnavailable
    
    var errorDescription: String? {
        switch self {
        case .emptyTheme:
            return "请输入故事主题"
        case .themeTooShort:
            return "故事主题太短，请至少输入2个字符"
        case .themeTooLong:
            return "故事主题太长，请不超过100个字符"
        case .inappropriateContent:
            return "主题内容不适合儿童，请重新输入"
        case .generationFailed(let message):
            return "故事生成失败: \(message)"
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        case .rateLimitExceeded:
            return "请求过于频繁，请稍后重试"
        case .serviceUnavailable:
            return "故事生成服务暂时不可用，请稍后重试"
        }
    }
}

/// 故事生成配置
struct StoryGenerationConfiguration {
    let maxTokens: Int
    let temperature: Double
    let topP: Double
    let maxRetries: Int
    let retryDelay: TimeInterval
    
    static let `default` = StoryGenerationConfiguration(
        maxTokens: 2000,
        temperature: 0.8,
        topP: 0.9,
        maxRetries: 3,
        retryDelay: 2.0
    )
    
    static let creative = StoryGenerationConfiguration(
        maxTokens: 2500,
        temperature: 0.9,
        topP: 0.95,
        maxRetries: 3,
        retryDelay: 2.0
    )
    
    static let conservative = StoryGenerationConfiguration(
        maxTokens: 1500,
        temperature: 0.7,
        topP: 0.85,
        maxRetries: 3,
        retryDelay: 2.0
    )
}
