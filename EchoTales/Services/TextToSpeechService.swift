//
//  TextToSpeechService.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import Foundation

/// 文本转语音服务实现
@MainActor
class TextToSpeechService: TextToSpeechServiceProtocol {
    
    // MARK: - Properties
    private let miniMaxService: MiniMaxService
    private var currentSynthesisTask: Task<Void, Never>?
    private var synthesisProgress: Double = 0.0
    
    // MARK: - Initialization
    init() {
        self.miniMaxService = MiniMaxService()
    }
    
    // MARK: - TextToSpeechServiceProtocol Implementation
    
    func synthesizeSpeech(text: String, voiceClone: VoiceClone) async throws -> URL {
        // 验证输入参数
        try validateInputs(text: text, voiceClone: voiceClone)
        
        // 重置进度
        synthesisProgress = 0.0
        
        do {
            // 更新进度
            synthesisProgress = 0.1
            
            // 分段处理长文本
            let textSegments = splitTextIntoSegments(text)
            synthesisProgress = 0.2
            
            var audioSegments: [Data] = []
            let segmentProgress = 0.6 / Double(textSegments.count)
            
            // 为每个文本段生成语音
            for (index, segment) in textSegments.enumerated() {
                let audioData = try await miniMaxService.synthesizeSpeech(
                    text: segment,
                    voiceId: voiceClone.cloneId!
                )
                audioSegments.append(audioData)
                
                // 更新进度
                synthesisProgress = 0.2 + Double(index + 1) * segmentProgress
            }
            
            // 合并音频段
            synthesisProgress = 0.8
            let mergedAudio = try mergeAudioSegments(audioSegments)
            
            // 保存到临时文件
            synthesisProgress = 0.9
            let audioURL = try saveAudioToFile(mergedAudio)
            
            // 完成
            synthesisProgress = 1.0
            
            return audioURL
            
        } catch {
            synthesisProgress = 0.0
            throw TextToSpeechError.synthesisError(error.localizedDescription)
        }
    }
    
    func getSynthesisProgress() async -> Double {
        return synthesisProgress
    }
    
    func cancelSynthesis() async throws {
        currentSynthesisTask?.cancel()
        currentSynthesisTask = nil
        synthesisProgress = 0.0
    }
    
    // MARK: - Private Methods
    
    /// 验证输入参数
    private func validateInputs(text: String, voiceClone: VoiceClone) throws {
        // 检查文本
        guard !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw TextToSpeechError.emptyText
        }
        
        // 检查文本长度
        guard text.count <= 10000 else {
            throw TextToSpeechError.textTooLong
        }
        
        // 检查语音克隆状态
        guard voiceClone.canUsedForSynthesis else {
            throw TextToSpeechError.voiceCloneNotReady
        }
    }
    
    /// 将长文本分割成段落
    private func splitTextIntoSegments(_ text: String) -> [String] {
        let maxSegmentLength = 500 // 每段最大字符数
        var segments: [String] = []
        
        // 按句子分割
        let sentences = text.components(separatedBy: CharacterSet(charactersIn: "。！？.!?"))
        
        var currentSegment = ""
        
        for sentence in sentences {
            let trimmedSentence = sentence.trimmingCharacters(in: .whitespacesAndNewlines)
            if trimmedSentence.isEmpty { continue }
            
            let potentialSegment = currentSegment.isEmpty ? trimmedSentence : "\(currentSegment)。\(trimmedSentence)"
            
            if potentialSegment.count <= maxSegmentLength {
                currentSegment = potentialSegment
            } else {
                // 当前段落已满，保存并开始新段落
                if !currentSegment.isEmpty {
                    segments.append(currentSegment)
                }
                currentSegment = trimmedSentence
            }
        }
        
        // 添加最后一个段落
        if !currentSegment.isEmpty {
            segments.append(currentSegment)
        }
        
        return segments.isEmpty ? [text] : segments
    }
    
    /// 合并音频段
    private func mergeAudioSegments(_ segments: [Data]) throws -> Data {
        guard !segments.isEmpty else {
            throw TextToSpeechError.audioProcessingError("没有音频段可合并")
        }
        
        if segments.count == 1 {
            return segments[0]
        }
        
        // 简单的音频合并（实际应用中可能需要更复杂的音频处理）
        var mergedData = Data()
        for segment in segments {
            mergedData.append(segment)
        }
        
        return mergedData
    }
    
    /// 保存音频到文件
    private func saveAudioToFile(_ audioData: Data) throws -> URL {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let fileName = "synthesized_\(Date().timeIntervalSince1970).mp3"
        let audioURL = documentsPath.appendingPathComponent(fileName)
        
        do {
            try audioData.write(to: audioURL)
            return audioURL
        } catch {
            throw TextToSpeechError.fileError("保存音频文件失败: \(error.localizedDescription)")
        }
    }
    
    /// 估算合成时间
    private func estimateSynthesisTime(for text: String) -> TimeInterval {
        // 根据文本长度估算合成时间
        let charactersPerSecond = 50.0 // 每秒处理50个字符
        return max(5.0, Double(text.count) / charactersPerSecond)
    }
    
    /// 清理临时文件
    func cleanupTemporaryFiles() {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        
        do {
            let files = try FileManager.default.contentsOfDirectory(at: documentsPath, includingPropertiesForKeys: nil)
            let synthesizedFiles = files.filter { $0.lastPathComponent.hasPrefix("synthesized_") }
            
            for file in synthesizedFiles {
                try? FileManager.default.removeItem(at: file)
            }
        } catch {
            print("清理临时文件失败: \(error)")
        }
    }
}

/// 文本转语音错误类型
enum TextToSpeechError: Error, LocalizedError {
    case emptyText
    case textTooLong
    case voiceCloneNotReady
    case synthesisError(String)
    case audioProcessingError(String)
    case fileError(String)
    case networkError(Error)
    case rateLimitExceeded
    case serviceUnavailable
    
    var errorDescription: String? {
        switch self {
        case .emptyText:
            return "文本内容不能为空"
        case .textTooLong:
            return "文本过长，请确保不超过10000个字符"
        case .voiceCloneNotReady:
            return "语音克隆尚未完成，请等待克隆完成后再试"
        case .synthesisError(let message):
            return "语音合成失败: \(message)"
        case .audioProcessingError(let message):
            return "音频处理失败: \(message)"
        case .fileError(let message):
            return "文件操作失败: \(message)"
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        case .rateLimitExceeded:
            return "请求过于频繁，请稍后重试"
        case .serviceUnavailable:
            return "语音合成服务暂时不可用，请稍后重试"
        }
    }
}

/// 文本转语音配置
struct TextToSpeechConfiguration {
    let speed: Double
    let volume: Double
    let responseFormat: String
    let maxSegmentLength: Int
    let enableNoiseReduction: Bool
    
    static let `default` = TextToSpeechConfiguration(
        speed: 1.0,
        volume: 1.0,
        responseFormat: "mp3",
        maxSegmentLength: 500,
        enableNoiseReduction: true
    )
    
    static let fast = TextToSpeechConfiguration(
        speed: 1.2,
        volume: 1.0,
        responseFormat: "mp3",
        maxSegmentLength: 500,
        enableNoiseReduction: true
    )
    
    static let slow = TextToSpeechConfiguration(
        speed: 0.8,
        volume: 1.0,
        responseFormat: "mp3",
        maxSegmentLength: 500,
        enableNoiseReduction: true
    )
    
    static let highQuality = TextToSpeechConfiguration(
        speed: 1.0,
        volume: 1.0,
        responseFormat: "wav",
        maxSegmentLength: 300,
        enableNoiseReduction: true
    )
}
