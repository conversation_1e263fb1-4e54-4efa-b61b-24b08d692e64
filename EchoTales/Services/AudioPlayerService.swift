//
//  AudioPlayerService.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import Foundation
import AVFoundation

/// 音频播放服务实现
@MainActor
class AudioPlayerService: NSObject, AudioPlayerServiceProtocol {
    
    // MARK: - Properties
    private var audioPlayer: AVAudioPlayer?
    private var audioSession: AVAudioSession
    private var playbackTimer: Timer?
    
    // MARK: - Public Properties
    var currentTime: TimeInterval {
        return audioPlayer?.currentTime ?? 0
    }
    
    var duration: TimeInterval {
        return audioPlayer?.duration ?? 0
    }
    
    var isPlaying: Bool {
        return audioPlayer?.isPlaying ?? false
    }
    
    var isPaused: Bool {
        return audioPlayer != nil && !isPlaying
    }
    
    // MARK: - Callbacks
    var onPlaybackProgress: ((TimeInterval, TimeInterval) -> Void)?
    var onPlaybackFinished: (() -> Void)?
    var onPlaybackError: ((Error) -> Void)?
    
    // MARK: - Initialization
    override init() {
        self.audioSession = AVAudioSession.sharedInstance()
        super.init()
        setupAudioSession()
    }
    
    // MARK: - Audio Session Setup
    private func setupAudioSession() {
        do {
            try audioSession.setCategory(.playback, mode: .default)
            try audioSession.setActive(true)
        } catch {
            print("Failed to setup audio session: \(error)")
        }
    }
    
    // MARK: - AudioPlayerServiceProtocol Implementation
    
    func play(url: URL) async throws {
        // 停止当前播放
        stop()
        
        // 验证文件存在
        guard FileManager.default.fileExists(atPath: url.path) else {
            throw AudioPlayerError.fileNotFound
        }
        
        do {
            // 创建音频播放器
            audioPlayer = try AVAudioPlayer(contentsOf: url)
            audioPlayer?.delegate = self
            audioPlayer?.prepareToPlay()
            
            // 开始播放
            let success = audioPlayer?.play() ?? false
            guard success else {
                throw AudioPlayerError.playbackFailed("播放启动失败")
            }
            
            // 启动进度定时器
            startProgressTimer()
            
        } catch {
            throw AudioPlayerError.playbackFailed("播放失败: \(error.localizedDescription)")
        }
    }
    
    func pause() {
        audioPlayer?.pause()
        stopProgressTimer()
    }
    
    func resume() {
        guard let player = audioPlayer else { return }
        
        if !player.isPlaying {
            player.play()
            startProgressTimer()
        }
    }
    
    func stop() {
        audioPlayer?.stop()
        audioPlayer = nil
        stopProgressTimer()
    }
    
    func seek(to time: TimeInterval) {
        guard let player = audioPlayer else { return }
        
        let clampedTime = max(0, min(time, player.duration))
        player.currentTime = clampedTime
        
        // 通知进度更新
        onPlaybackProgress?(clampedTime, player.duration)
    }
    
    // MARK: - Progress Timer
    private func startProgressTimer() {
        stopProgressTimer()
        
        playbackTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateProgress()
            }
        }
    }
    
    private func stopProgressTimer() {
        playbackTimer?.invalidate()
        playbackTimer = nil
    }
    
    private func updateProgress() {
        guard let player = audioPlayer else { return }
        onPlaybackProgress?(player.currentTime, player.duration)
    }
    
    // MARK: - Volume Control
    func setVolume(_ volume: Float) {
        audioPlayer?.volume = max(0.0, min(1.0, volume))
    }
    
    func getVolume() -> Float {
        return audioPlayer?.volume ?? 1.0
    }
    
    // MARK: - Playback Rate
    func setPlaybackRate(_ rate: Float) {
        guard let player = audioPlayer else { return }
        
        if player.enableRate {
            player.rate = max(0.5, min(2.0, rate))
        }
    }
    
    func getPlaybackRate() -> Float {
        return audioPlayer?.rate ?? 1.0
    }
    
    // MARK: - Audio Information
    func getAudioInfo(for url: URL) throws -> AudioInfo {
        do {
            let player = try AVAudioPlayer(contentsOf: url)
            return AudioInfo(
                duration: player.duration,
                numberOfChannels: player.numberOfChannels,
                format: player.format.description
            )
        } catch {
            throw AudioPlayerError.fileError("无法读取音频信息: \(error.localizedDescription)")
        }
    }
    
    // MARK: - Cleanup
    deinit {
        stop()
    }
}

// MARK: - AVAudioPlayerDelegate
extension AudioPlayerService: AVAudioPlayerDelegate {
    func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        stopProgressTimer()
        
        if flag {
            onPlaybackFinished?()
        } else {
            onPlaybackError?(AudioPlayerError.playbackFailed("播放未正常完成"))
        }
    }
    
    func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        stopProgressTimer()
        
        if let error = error {
            onPlaybackError?(AudioPlayerError.playbackFailed("音频解码错误: \(error.localizedDescription)"))
        }
    }
}

/// 音频播放错误类型
enum AudioPlayerError: Error, LocalizedError {
    case fileNotFound
    case fileError(String)
    case playbackFailed(String)
    case invalidFormat
    case deviceError
    
    var errorDescription: String? {
        switch self {
        case .fileNotFound:
            return "音频文件不存在"
        case .fileError(let message):
            return "文件错误: \(message)"
        case .playbackFailed(let message):
            return "播放失败: \(message)"
        case .invalidFormat:
            return "不支持的音频格式"
        case .deviceError:
            return "音频设备错误"
        }
    }
}

/// 音频信息
struct AudioInfo {
    let duration: TimeInterval
    let numberOfChannels: Int
    let format: String
    
    var formattedDuration: String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
}
