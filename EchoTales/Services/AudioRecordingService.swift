//
//  AudioRecordingService.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import Foundation
import AVFoundation

/// 录音服务实现
@MainActor
class AudioRecordingService: NSObject, AudioRecordingServiceProtocol {
    
    // MARK: - Properties
    private var audioRecorder: AVAudioRecorder?
    private var audioPlayer: AVAudioPlayer?
    private var recordingSession: AVAudioSession
    private var currentRecording: AudioRecording?
    private var recordingTimer: Timer?
    private var autoStopTimer: Timer?
    private var recordingStartTime: Date?

    // 固定录音时长：15秒
    private let maxRecordingDuration: TimeInterval = 15.0
    
    // MARK: - Public Properties
    var isRecording: Bool {
        return audioRecorder?.isRecording ?? false
    }
    
    var isPlaying: Bool {
        return audioPlayer?.isPlaying ?? false
    }
    
    // MARK: - Initialization
    override init() {
        self.recordingSession = AVAudioSession.sharedInstance()
        super.init()
        setupAudioSession()
    }
    
    // MARK: - Audio Session Setup
    private func setupAudioSession() {
        do {
            try recordingSession.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker, .allowBluetooth])
            try recordingSession.setActive(true)
            print("音频会话设置成功")
        } catch {
            print("音频会话设置失败: \(error)")
        }
    }
    
    // MARK: - Permission Management
    func requestMicrophonePermission() async -> Bool {
        return await withCheckedContinuation { continuation in
            if #available(iOS 17.0, *) {
                AVAudioApplication.requestRecordPermission { granted in
                    continuation.resume(returning: granted)
                }
            } else {
                recordingSession.requestRecordPermission { granted in
                    continuation.resume(returning: granted)
                }
            }
        }
    }
    
    // MARK: - Recording Management
    func startRecording(language: Language) async throws -> AudioRecording {
        print("开始录音请求...")

        // 检查权限
        let hasPermission = await requestMicrophonePermission()
        print("麦克风权限状态: \(hasPermission)")
        guard hasPermission else {
            throw NSError(domain: "AudioRecordingService", code: 1001, userInfo: [NSLocalizedDescriptionKey: "麦克风权限被拒绝"])
        }

        // 停止当前录音（如果有）
        if isRecording {
            print("停止当前录音...")
            try await stopRecording()
        }
        
        // 创建录音文件路径
        let fileName = "recording_\(Date().timeIntervalSince1970).m4a"
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let audioURL = documentsPath.appendingPathComponent(fileName)
        
        // 配置录音设置
        let settings: [String: Any] = [
            AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
            AVSampleRateKey: 44100,
            AVNumberOfChannelsKey: 1,
            AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue
        ]
        
        do {
            // 创建录音器
            audioRecorder = try AVAudioRecorder(url: audioURL, settings: settings)
            audioRecorder?.delegate = self
            audioRecorder?.isMeteringEnabled = true
            
            // 开始录音
            let success = audioRecorder?.record() ?? false
            guard success else {
                throw NSError(domain: "AudioRecordingService", code: 1002, userInfo: [NSLocalizedDescriptionKey: "录音启动失败"])
            }
            
            // 创建录音对象
            currentRecording = AudioRecording(
                fileName: fileName,
                filePath: audioURL,
                language: language
            )
            
            recordingStartTime = Date()
            startRecordingTimer()
            startAutoStopTimer()

            return currentRecording!
            
        } catch {
            throw NSError(domain: "AudioRecordingService", code: 1003, userInfo: [NSLocalizedDescriptionKey: "录音创建失败: \(error.localizedDescription)"])
        }
    }
    
    func stopRecording() async throws {
        guard let recorder = audioRecorder, recorder.isRecording else {
            return
        }
        
        recorder.stop()
        stopRecordingTimer()
        stopAutoStopTimer()

        // 更新录音时长
        if let recording = currentRecording, let startTime = recordingStartTime {
            recording.duration = Date().timeIntervalSince(startTime)
        }

        audioRecorder = nil
        recordingStartTime = nil
    }
    
    func getRecordingDuration() -> TimeInterval {
        guard let startTime = recordingStartTime else { return 0 }
        return Date().timeIntervalSince(startTime)
    }
    
    // MARK: - Playback Management
    func playRecording(_ recording: AudioRecording) async throws {
        guard let audioURL = recording.filePath else {
            throw NSError(domain: "AudioRecordingService", code: 1004, userInfo: [NSLocalizedDescriptionKey: "录音文件路径无效"])
        }
        
        guard FileManager.default.fileExists(atPath: audioURL.path) else {
            throw NSError(domain: "AudioRecordingService", code: 1005, userInfo: [NSLocalizedDescriptionKey: "录音文件不存在"])
        }
        
        do {
            // 停止当前播放
            stopPlaying()
            
            // 创建播放器
            audioPlayer = try AVAudioPlayer(contentsOf: audioURL)
            audioPlayer?.delegate = self
            audioPlayer?.prepareToPlay()
            
            // 开始播放
            let success = audioPlayer?.play() ?? false
            guard success else {
                throw NSError(domain: "AudioRecordingService", code: 1006, userInfo: [NSLocalizedDescriptionKey: "播放启动失败"])
            }
            
        } catch {
            throw NSError(domain: "AudioRecordingService", code: 1007, userInfo: [NSLocalizedDescriptionKey: "播放失败: \(error.localizedDescription)"])
        }
    }
    
    func stopPlaying() {
        audioPlayer?.stop()
        audioPlayer = nil
    }
    
    // MARK: - File Management
    func deleteRecording(_ recording: AudioRecording) async throws {
        // 停止播放（如果正在播放这个录音）
        if isPlaying {
            stopPlaying()
        }
        
        // 删除文件
        try recording.deleteFile()
    }
    
    // MARK: - Timer Management
    private func startRecordingTimer() {
        recordingTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            self?.updateRecordingMeters()
        }
    }
    
    private func stopRecordingTimer() {
        recordingTimer?.invalidate()
        recordingTimer = nil
    }

    private func startAutoStopTimer() {
        autoStopTimer = Timer.scheduledTimer(withTimeInterval: maxRecordingDuration, repeats: false) { [weak self] _ in
            Task { @MainActor in
                do {
                    try await self?.stopRecording()
                    print("录音已自动停止（达到15秒限制）")
                } catch {
                    print("自动停止录音失败: \(error)")
                }
            }
        }
    }

    private func stopAutoStopTimer() {
        autoStopTimer?.invalidate()
        autoStopTimer = nil
    }
    
    private func updateRecordingMeters() {
        audioRecorder?.updateMeters()
        // 这里可以发送通知或更新UI，显示录音音量等信息
    }
    
    // MARK: - Audio Level
    func getAudioLevel() -> Float {
        guard let recorder = audioRecorder, recorder.isRecording else { return 0.0 }
        recorder.updateMeters()
        return recorder.averagePower(forChannel: 0)
    }
}

// MARK: - AVAudioRecorderDelegate
extension AudioRecordingService: @preconcurrency AVAudioRecorderDelegate {
    func audioRecorderDidFinishRecording(_ recorder: AVAudioRecorder, successfully flag: Bool) {
        if !flag {
            print("Recording finished unsuccessfully")
        }
    }
    
    func audioRecorderEncodeErrorDidOccur(_ recorder: AVAudioRecorder, error: Error?) {
        if let error = error {
            print("Recording encode error: \(error)")
        }
    }
}

// MARK: - AVAudioPlayerDelegate
extension AudioRecordingService: @preconcurrency AVAudioPlayerDelegate {
    func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        audioPlayer = nil
    }
    
    func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        if let error = error {
            print("Playback decode error: \(error)")
        }
        audioPlayer = nil
    }
}
