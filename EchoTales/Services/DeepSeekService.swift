//
//  DeepSeekService.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import Foundation

/// DeepSeek聊天消息
struct ChatMessage: Codable {
    let role: String
    let content: String
}

/// DeepSeek聊天完成请求
struct ChatCompletionRequest: Codable {
    let model: String
    let messages: [ChatMessage]
    let maxTokens: Int?
    let temperature: Double?
    let topP: Double?
    let stream: Bool?
    
    enum CodingKeys: String, CodingKey {
        case model
        case messages
        case maxTokens = "max_tokens"
        case temperature
        case topP = "top_p"
        case stream
    }
}

/// DeepSeek聊天完成响应
struct ChatCompletionResponse: Codable {
    let id: String
    let object: String
    let created: Int
    let model: String
    let choices: [ChatChoice]
    let usage: TokenUsage?
}

struct ChatChoice: Codable {
    let index: Int
    let message: ChatMessage
    let finishReason: String?
    
    enum CodingKeys: String, CodingKey {
        case index
        case message
        case finishReason = "finish_reason"
    }
}

struct TokenUsage: Codable {
    let promptTokens: Int
    let completionTokens: Int
    let totalTokens: Int
    
    enum CodingKeys: String, CodingKey {
        case promptTokens = "prompt_tokens"
        case completionTokens = "completion_tokens"
        case totalTokens = "total_tokens"
    }
}

/// DeepSeek API服务
@MainActor
class DeepSeekService {
    private let baseURL = "https://api.deepseek.com/v1"
    private let networkService = NetworkService.shared

    // API配置 - 从UserDefaults中获取
    private var apiKey: String {
        return UserDefaults.standard.string(forKey: "deepseek_api_key") ?? ""
    }
    
    /// 生成童话故事
    func generateStory(theme: String, language: Language, maxTokens: Int = 2000) async throws -> String {
        // 检查API Key是否配置
        guard !apiKey.isEmpty else {
            throw NetworkError.authenticationFailed
        }

        guard let url = URL(string: "\(baseURL)/chat/completions") else {
            throw NetworkError.invalidURL
        }
        
        let systemPrompt = createSystemPrompt(for: language)
        let userPrompt = createUserPrompt(theme: theme, language: language)
        
        let messages = [
            ChatMessage(role: "system", content: systemPrompt),
            ChatMessage(role: "user", content: userPrompt)
        ]
        
        let request = ChatCompletionRequest(
            model: "deepseek-chat",
            messages: messages,
            maxTokens: maxTokens,
            temperature: 0.8,
            topP: 0.9,
            stream: false
        )
        
        let headers = [
            "Authorization": "Bearer \(apiKey)",
            "Content-Type": "application/json"
        ]
        
        let body = try networkService.encodeBody(request)
        let networkRequest = NetworkRequest(url: url, method: .POST, headers: headers, body: body)
        
        let response: ChatCompletionResponse = try await networkService.performRequest(networkRequest, responseType: ChatCompletionResponse.self)
        
        guard let choice = response.choices.first else {
            throw NetworkError.noData
        }
        
        return choice.message.content
    }
    
    /// 创建系统提示词
    private func createSystemPrompt(for language: Language) -> String {
        switch language.code {
        case "zh-CN":
            return """
            你是一个专业的童话故事创作者。请创作适合儿童的温馨童话故事，要求：
            1. 故事内容积极正面，传递正能量
            2. 语言简洁易懂，适合儿童理解
            3. 情节生动有趣，富有想象力
            4. 故事长度适中，大约500-800字
            5. 包含明确的道德寓意或教育意义
            6. 避免暴力、恐怖或不适合儿童的内容
            
            请用中文创作故事。
            """
        case "zh-HK":
            return """
            你是一個專業的童話故事創作者。請創作適合兒童的溫馨童話故事，要求：
            1. 故事內容積極正面，傳遞正能量
            2. 語言簡潔易懂，適合兒童理解
            3. 情節生動有趣，富有想像力
            4. 故事長度適中，大約500-800字
            5. 包含明確的道德寓意或教育意義
            6. 避免暴力、恐怖或不適合兒童的內容
            
            請用繁體中文（粵語）創作故事。
            """
        case "en-US":
            return """
            You are a professional fairy tale writer. Please create warm and child-friendly fairy tales with the following requirements:
            1. Positive and uplifting content that spreads positive energy
            2. Simple and easy-to-understand language suitable for children
            3. Vivid and interesting plot with rich imagination
            4. Moderate story length, approximately 500-800 words
            5. Clear moral lessons or educational significance
            6. Avoid violence, horror, or content inappropriate for children
            
            Please create the story in English.
            """
        default:
            return """
            You are a professional fairy tale writer. Please create warm and child-friendly fairy tales with the following requirements:
            1. Positive and uplifting content that spreads positive energy
            2. Simple and easy-to-understand language suitable for children
            3. Vivid and interesting plot with rich imagination
            4. Moderate story length, approximately 500-800 words
            5. Clear moral lessons or educational significance
            6. Avoid violence, horror, or content inappropriate for children
            """
        }
    }
    
    /// 创建用户提示词
    private func createUserPrompt(theme: String, language: Language) -> String {
        switch language.code {
        case "zh-CN":
            return "请以「\(theme)」为主题，创作一个温馨的童话故事。故事要有完整的开头、发展、高潮和结尾，并且要有明确的标题。"
        case "zh-HK":
            return "請以「\(theme)」為主題，創作一個溫馨的童話故事。故事要有完整的開頭、發展、高潮和結尾，並且要有明確的標題。"
        case "en-US":
            return "Please create a warm fairy tale with the theme of '\(theme)'. The story should have a complete beginning, development, climax, and ending, with a clear title."
        default:
            return "Please create a warm fairy tale with the theme of '\(theme)'. The story should have a complete beginning, development, climax, and ending, with a clear title."
        }
    }
    
    /// 提取故事标题和内容
    func parseStoryContent(_ content: String) -> (title: String, story: String) {
        let lines = content.components(separatedBy: .newlines)
        var title = ""
        var story = ""
        
        // 尝试找到标题
        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
            if !trimmedLine.isEmpty {
                // 如果是第一行非空行，可能是标题
                if title.isEmpty {
                    // 移除可能的标题标记
                    title = trimmedLine
                        .replacingOccurrences(of: "# ", with: "")
                        .replacingOccurrences(of: "## ", with: "")
                        .replacingOccurrences(of: "**", with: "")
                        .replacingOccurrences(of: "标题：", with: "")
                        .replacingOccurrences(of: "Title: ", with: "")
                } else {
                    // 其余内容作为故事正文
                    if !story.isEmpty {
                        story += "\n"
                    }
                    story += trimmedLine
                }
            }
        }
        
        // 如果没有找到明确的标题，使用默认标题
        if title.isEmpty {
            title = "童话故事"
            story = content
        }
        
        return (title: title, story: story)
    }
}
