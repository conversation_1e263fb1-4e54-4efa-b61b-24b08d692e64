//
//  VoiceClone.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import Foundation

/// 语音克隆状态
enum VoiceCloneStatus: String, Codable, CaseIterable {
    case pending = "pending"
    case processing = "processing"
    case completed = "completed"
    case failed = "failed"
    
    var displayName: String {
        switch self {
        case .pending:
            return "等待中"
        case .processing:
            return "处理中"
        case .completed:
            return "已完成"
        case .failed:
            return "失败"
        }
    }
    
    var isCompleted: Bool {
        return self == .completed
    }
    
    var isFailed: Bool {
        return self == .failed
    }
}

/// 语音克隆数据模型
@Observable
class VoiceClone: Identifiable, Codable {
    let id = UUID()
    var name: String
    var language: Language
    var audioRecording: AudioRecording
    var status: VoiceCloneStatus
    var cloneId: String?
    var createdAt: Date
    var completedAt: Date?
    var errorMessage: String?
    var progress: Double
    
    init(name: String, language: Language, audioRecording: AudioRecording) {
        self.name = name
        self.language = language
        self.audioRecording = audioRecording
        self.status = .pending
        self.createdAt = Date()
        self.progress = 0.0
    }
    
    /// 更新克隆状态
    func updateStatus(_ newStatus: VoiceCloneStatus, progress: Double = 0.0, errorMessage: String? = nil) {
        self.status = newStatus
        self.progress = progress
        self.errorMessage = errorMessage
        
        if newStatus == .completed {
            self.completedAt = Date()
            self.progress = 1.0
        }
    }
    
    /// 设置克隆ID
    func setCloneId(_ cloneId: String) {
        self.cloneId = cloneId
    }
    
    /// 是否可以用于语音合成
    var canUsedForSynthesis: Bool {
        return status.isCompleted && cloneId != nil
    }
    
    /// 格式化的创建时间
    var formattedCreatedAt: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: createdAt)
    }
}

extension VoiceClone: Equatable {
    static func == (lhs: VoiceClone, rhs: VoiceClone) -> Bool {
        return lhs.id == rhs.id
    }
}

extension VoiceClone: Hashable {
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
}
