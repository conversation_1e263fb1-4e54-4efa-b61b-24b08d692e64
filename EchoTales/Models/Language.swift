//
//  Language.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import Foundation

/// 支持的语言类型
@Observable
class Language: Identifiable, Codable {
    let id = UUID()
    let code: String
    let name: String
    let displayName: String
    let isSupported: Bool
    
    init(code: String, name: String, displayName: String, isSupported: Bool = true) {
        self.code = code
        self.name = name
        self.displayName = displayName
        self.isSupported = isSupported
    }
    
    /// 支持的语言列表
    static let supportedLanguages: [Language] = [
        Language(code: "zh-CN", name: "Chinese (Mandarin)", displayName: "中文（普通话）"),
        Language(code: "zh-HK", name: "Chinese (Cantonese)", displayName: "中文（粤语）"),
        Language(code: "en-US", name: "English (US)", displayName: "English")
    ]
    
    /// 根据代码获取语言
    static func language(for code: String) -> Language? {
        return supportedLanguages.first { $0.code == code }
    }
}

extension Language: Equatable {
    static func == (lhs: Language, rhs: Language) -> Bool {
        return lhs.code == rhs.code
    }
}

extension Language: Hashable {
    func hash(into hasher: inout Hasher) {
        hasher.combine(code)
    }
}
