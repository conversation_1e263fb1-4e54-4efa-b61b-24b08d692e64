//
//  Story.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import Foundation
import SwiftUI

/// 故事生成状态
enum StoryStatus: String, Codable, CaseIterable {
    case pending = "pending"
    case generating = "generating"
    case completed = "completed"
    case failed = "failed"
    
    var displayName: String {
        switch self {
        case .pending:
            return "等待中"
        case .generating:
            return "生成中"
        case .completed:
            return "已完成"
        case .failed:
            return "失败"
        }
    }
    
    var isCompleted: Bool {
        return self == .completed
    }
    
    var isFailed: Bool {
        return self == .failed
    }

    var color: Color {
        switch self {
        case .pending:
            return .gray
        case .generating:
            return .blue
        case .completed:
            return .green
        case .failed:
            return .red
        }
    }

    var icon: String {
        switch self {
        case .pending:
            return "clock"
        case .generating:
            return "gearshape"
        case .completed:
            return "checkmark.circle.fill"
        case .failed:
            return "xmark.circle.fill"
        }
    }
}

/// 故事数据模型
@Observable
class Story: Identifiable, Codable {
    let id = UUID()
    var title: String
    var content: String
    var theme: String
    var language: Language
    var status: StoryStatus
    var createdAt: Date
    var completedAt: Date?
    var errorMessage: String?
    var progress: Double
    var audioUrl: URL?
    var isAudioGenerated: Bool
    var audioGenerationProgress: Double
    
    init(title: String = "", theme: String, language: Language) {
        self.title = title
        self.content = ""
        self.theme = theme
        self.language = language
        self.status = .pending
        self.createdAt = Date()
        self.progress = 0.0
        self.isAudioGenerated = false
        self.audioGenerationProgress = 0.0
    }
    
    /// 更新故事状态
    func updateStatus(_ newStatus: StoryStatus, progress: Double = 0.0, errorMessage: String? = nil) {
        self.status = newStatus
        self.progress = progress
        self.errorMessage = errorMessage
        
        if newStatus == .completed {
            self.completedAt = Date()
            self.progress = 1.0
        }
    }
    
    /// 设置故事内容
    func setContent(_ content: String, title: String? = nil) {
        self.content = content
        if let title = title {
            self.title = title
        }
    }
    
    /// 设置音频URL
    func setAudioUrl(_ url: URL) {
        self.audioUrl = url
        self.isAudioGenerated = true
        self.audioGenerationProgress = 1.0
    }
    
    /// 更新音频生成进度
    func updateAudioProgress(_ progress: Double) {
        self.audioGenerationProgress = progress
    }
    
    /// 是否可以播放音频
    var canPlayAudio: Bool {
        return isAudioGenerated && audioUrl != nil
    }
    
    /// 格式化的创建时间
    var formattedCreatedAt: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: createdAt)
    }
    
    /// 故事字数
    var wordCount: Int {
        return content.count
    }
    
    /// 预估阅读时间（分钟）
    var estimatedReadingTime: Int {
        let wordsPerMinute = language.code.hasPrefix("zh") ? 300 : 200
        return max(1, wordCount / wordsPerMinute)
    }

    // MARK: - Sample Data
    static let sampleStory = Story(
        theme: "勇敢的小兔子",
        language: Language.supportedLanguages[0]
    ).apply {
        $0.setContent(
            """
            从前，在一个美丽的森林里，住着一只名叫小白的兔子。小白有着雪白的毛发和红宝石般的眼睛，但是它有一个小秘密——它非常胆小。

            每当听到一点点声音，小白就会躲到妈妈身后。森林里的其他小动物都很勇敢，只有小白总是害怕这害怕那。

            有一天，森林里来了一只迷路的小鸟。小鸟哭着说："我找不到回家的路了，天快黑了，我好害怕。"

            其他小动物都在忙自己的事情，没有注意到小鸟的求助。只有小白听到了，虽然它也很害怕，但是看到小鸟比自己更害怕，小白鼓起勇气说："别哭，我来帮你找回家的路。"

            小白带着小鸟在森林里寻找，虽然路上遇到了很多困难，但小白都勇敢地克服了。最终，它们找到了小鸟的家。

            小鸟感激地说："谢谢你，小白！你真是一只勇敢的兔子！"

            从那以后，小白发现自己其实很勇敢，只是以前没有机会表现出来。森林里的小动物们也都夸奖小白是最勇敢的兔子。
            """,
            title: "勇敢的小兔子"
        )
        $0.updateStatus(.completed, progress: 1.0)
    }
}

extension Story {
    func apply(_ closure: (Story) -> Void) -> Story {
        closure(self)
        return self
    }
}

extension Story: Equatable {
    static func == (lhs: Story, rhs: Story) -> Bool {
        return lhs.id == rhs.id
    }
}

extension Story: Hashable {
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
}
