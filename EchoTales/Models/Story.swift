//
//  Story.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import Foundation

/// 故事生成状态
enum StoryStatus: String, Codable, CaseIterable {
    case pending = "pending"
    case generating = "generating"
    case completed = "completed"
    case failed = "failed"
    
    var displayName: String {
        switch self {
        case .pending:
            return "等待中"
        case .generating:
            return "生成中"
        case .completed:
            return "已完成"
        case .failed:
            return "失败"
        }
    }
    
    var isCompleted: Bool {
        return self == .completed
    }
    
    var isFailed: Bool {
        return self == .failed
    }
}

/// 故事数据模型
@Observable
class Story: Identifiable, Codable {
    let id = UUID()
    var title: String
    var content: String
    var theme: String
    var language: Language
    var status: StoryStatus
    var createdAt: Date
    var completedAt: Date?
    var errorMessage: String?
    var progress: Double
    var audioUrl: URL?
    var isAudioGenerated: Bool
    var audioGenerationProgress: Double
    
    init(title: String = "", theme: String, language: Language) {
        self.title = title
        self.content = ""
        self.theme = theme
        self.language = language
        self.status = .pending
        self.createdAt = Date()
        self.progress = 0.0
        self.isAudioGenerated = false
        self.audioGenerationProgress = 0.0
    }
    
    /// 更新故事状态
    func updateStatus(_ newStatus: StoryStatus, progress: Double = 0.0, errorMessage: String? = nil) {
        self.status = newStatus
        self.progress = progress
        self.errorMessage = errorMessage
        
        if newStatus == .completed {
            self.completedAt = Date()
            self.progress = 1.0
        }
    }
    
    /// 设置故事内容
    func setContent(_ content: String, title: String? = nil) {
        self.content = content
        if let title = title {
            self.title = title
        }
    }
    
    /// 设置音频URL
    func setAudioUrl(_ url: URL) {
        self.audioUrl = url
        self.isAudioGenerated = true
        self.audioGenerationProgress = 1.0
    }
    
    /// 更新音频生成进度
    func updateAudioProgress(_ progress: Double) {
        self.audioGenerationProgress = progress
    }
    
    /// 是否可以播放音频
    var canPlayAudio: Bool {
        return isAudioGenerated && audioUrl != nil
    }
    
    /// 格式化的创建时间
    var formattedCreatedAt: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: createdAt)
    }
    
    /// 故事字数
    var wordCount: Int {
        return content.count
    }
    
    /// 预估阅读时间（分钟）
    var estimatedReadingTime: Int {
        let wordsPerMinute = language.code.hasPrefix("zh") ? 300 : 200
        return max(1, wordCount / wordsPerMinute)
    }
}

extension Story: Equatable {
    static func == (lhs: Story, rhs: Story) -> Bool {
        return lhs.id == rhs.id
    }
}

extension Story: Hashable {
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
}
