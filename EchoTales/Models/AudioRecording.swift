//
//  AudioRecording.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import Foundation
import AVFoundation

/// 录音数据模型
@Observable
class AudioRecording: Identifiable, Codable {
    let id = UUID()
    var fileName: String
    var filePath: URL?
    var duration: TimeInterval
    var language: Language
    var createdAt: Date
    var isUploaded: Bool
    var uploadProgress: Double
    
    init(fileName: String, filePath: URL? = nil, duration: TimeInterval = 0, language: Language, createdAt: Date = Date()) {
        self.fileName = fileName
        self.filePath = filePath
        self.duration = duration
        self.language = language
        self.createdAt = createdAt
        self.isUploaded = false
        self.uploadProgress = 0.0
    }
    
    /// 录音文件的完整路径
    var fullPath: URL? {
        guard let filePath = filePath else { return nil }
        return filePath
    }
    
    /// 录音文件是否存在
    var fileExists: Bool {
        guard let path = fullPath else { return false }
        return FileManager.default.fileExists(atPath: path.path)
    }
    
    /// 格式化的时长显示
    var formattedDuration: String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    /// 格式化的文件大小
    var formattedFileSize: String {
        guard let path = fullPath else { return "未知" }

        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: path.path)
            if let fileSize = attributes[.size] as? Int64 {
                return ByteCountFormatter.string(fromByteCount: fileSize, countStyle: .file)
            }
        } catch {
            print("获取文件大小失败: \(error)")
        }

        return "未知"
    }

    /// 删除录音文件
    func deleteFile() throws {
        guard let path = fullPath else { return }
        try FileManager.default.removeItem(at: path)
    }
}

extension AudioRecording: Equatable {
    static func == (lhs: AudioRecording, rhs: AudioRecording) -> Bool {
        return lhs.id == rhs.id
    }
}

extension AudioRecording: Hashable {
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
}
