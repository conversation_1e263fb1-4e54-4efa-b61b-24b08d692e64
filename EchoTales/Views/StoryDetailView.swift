//
//  StoryDetailView.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import SwiftUI

/// 故事详情视图
struct StoryDetailView: View {
    let story: Story
    @Environment(\.dismiss) private var dismiss
    @Environment(AppViewModel.self) private var appViewModel
    @State private var isPlaying = false
    @State private var playbackProgress: Double = 0.0
    @State private var showingShareSheet = false
    @State private var shareText = ""
    
    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 故事标题和信息
                storyHeaderSection
                
                // 播放控制
                playbackControlSection
                
                // 故事内容
                storyContentSection
                
                // 操作按钮
                actionButtonsSection
            }
            .padding()
        }
        .navigationTitle("故事详情")
        .navigationBarTitleDisplayMode(.inline)
        .sheet(isPresented: $showingShareSheet) {
            ShareSheet(activityItems: [shareText])
        }
    }
    
    private var storyHeaderSection: some View {
        VStack(spacing: 16) {
            // 故事图标
            Image(systemName: "book.circle.fill")
                .font(.system(size: 80))
                .foregroundStyle(.blue.gradient)
            
            VStack(spacing: 8) {
                Text(story.title)
                    .font(.title)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)
                
                Text(story.theme)
                    .font(.headline)
                    .foregroundStyle(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            // 故事信息
            HStack(spacing: 20) {
                VStack(spacing: 4) {
                    Text("\(story.wordCount)")
                        .font(.title2)
                        .fontWeight(.bold)
                    Text("字数")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
                
                VStack(spacing: 4) {
                    Text("\(story.estimatedReadingTime)")
                        .font(.title2)
                        .fontWeight(.bold)
                    Text("分钟")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
                
                VStack(spacing: 4) {
                    Text(story.language.name)
                        .font(.title2)
                        .fontWeight(.bold)
                    Text("语言")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
            }
            .padding()
            .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 12))
        }
    }
    
    private var playbackControlSection: some View {
        VStack(spacing: 16) {
            Text("语音播放")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                // 播放进度条
                VStack(spacing: 8) {
                    ProgressView(value: playbackProgress)
                        .progressViewStyle(.linear)
                        .tint(.blue)
                    
                    HStack {
                        Text(formatTime(playbackProgress * Double(story.estimatedReadingTime * 60)))
                            .font(.caption)
                            .foregroundStyle(.secondary)
                        
                        Spacer()
                        
                        Text(formatTime(Double(story.estimatedReadingTime * 60)))
                            .font(.caption)
                            .foregroundStyle(.secondary)
                    }
                }
                
                // 播放控制按钮
                HStack(spacing: 24) {
                    Button {
                        // 后退15秒
                    } label: {
                        Image(systemName: "gobackward.15")
                            .font(.title2)
                            .foregroundStyle(.blue)
                    }
                    
                    Button {
                        isPlaying.toggle()
                        // 播放/暂停逻辑
                    } label: {
                        Image(systemName: isPlaying ? "pause.circle.fill" : "play.circle.fill")
                            .font(.system(size: 64))
                            .foregroundStyle(.blue)
                    }
                    
                    Button {
                        // 前进15秒
                    } label: {
                        Image(systemName: "goforward.15")
                            .font(.title2)
                            .foregroundStyle(.blue)
                    }
                }
                
                // 播放速度控制
                HStack {
                    Text("播放速度:")
                        .font(.subheadline)
                        .foregroundStyle(.secondary)
                    
                    Spacer()
                    
                    Menu {
                        Button("0.5x") { }
                        Button("0.75x") { }
                        Button("1.0x") { }
                        Button("1.25x") { }
                        Button("1.5x") { }
                        Button("2.0x") { }
                    } label: {
                        HStack {
                            Text("1.0x")
                                .font(.subheadline)
                                .foregroundStyle(.blue)
                            
                            Image(systemName: "chevron.up.chevron.down")
                                .font(.caption)
                                .foregroundStyle(.blue)
                        }
                    }
                }
            }
            .padding()
            .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 12))
        }
    }
    
    private var storyContentSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("故事内容")
                .font(.headline)
                .fontWeight(.semibold)
            
            ScrollView {
                Text(story.content)
                    .font(.body)
                    .lineSpacing(6)
                    .padding()
                    .frame(maxWidth: .infinity, alignment: .leading)
            }
            .frame(maxHeight: 300)
            .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 12))
        }
    }
    
    private var actionButtonsSection: some View {
        VStack(spacing: 12) {
            // 主要操作
            HStack(spacing: 12) {
                Button {
                    shareText = """
                    \(story.title)
                    
                    \(story.content)
                    
                    ——来自 EchoTales
                    """
                    showingShareSheet = true
                } label: {
                    Label("分享故事", systemImage: "square.and.arrow.up")
                        .font(.headline)
                        .foregroundStyle(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(.green.gradient)
                        )
                }
                .buttonStyle(.plain)
                
                Button {
                    // 收藏故事
                } label: {
                    Label("收藏", systemImage: "heart")
                        .font(.headline)
                        .foregroundStyle(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(.pink.gradient)
                        )
                }
                .buttonStyle(.plain)
            }
            
            // 次要操作
            HStack(spacing: 12) {
                Button {
                    // 重新生成语音
                } label: {
                    Label("重新生成语音", systemImage: "waveform")
                        .font(.subheadline)
                        .foregroundStyle(.blue)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(.blue.opacity(0.1))
                        )
                }
                .buttonStyle(.plain)
                
                Button {
                    // 编辑故事
                } label: {
                    Label("编辑", systemImage: "pencil")
                        .font(.subheadline)
                        .foregroundStyle(.orange)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(.orange.opacity(0.1))
                        )
                }
                .buttonStyle(.plain)
            }
            
            // 删除按钮
            Button(role: .destructive) {
                appViewModel.deleteStory(story)
                dismiss()
            } label: {
                Label("删除故事", systemImage: "trash")
                    .font(.headline)
                    .foregroundStyle(.red)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(.red.opacity(0.1))
                    )
            }
            .buttonStyle(.plain)
        }
    }
    
    private func formatTime(_ seconds: Double) -> String {
        let minutes = Int(seconds) / 60
        let remainingSeconds = Int(seconds) % 60
        return String(format: "%02d:%02d", minutes, remainingSeconds)
    }
}

/// 分享表单
struct ShareSheet: UIViewControllerRepresentable {
    let activityItems: [Any]
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(
            activityItems: activityItems,
            applicationActivities: nil
        )
        return controller
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {
        // 不需要更新
    }
}

#Preview {
    NavigationStack {
        StoryDetailView(story: Story.sampleStory)
            .environment(AppViewModel())
    }
}
