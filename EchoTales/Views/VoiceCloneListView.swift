//
//  VoiceCloneListView.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import SwiftUI

/// 语音克隆列表视图
struct VoiceCloneListView: View {
    @Environment(AppViewModel.self) private var appViewModel
    @State private var showingRecordingView = false
    @State private var selectedVoiceClone: VoiceClone?
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                if appViewModel.voiceClones.isEmpty {
                    emptyStateView
                } else {
                    voiceCloneListContent
                }
            }
            .navigationTitle("我的声音")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .topBarTrailing) {
                    Button {
                        showingRecordingView = true
                    } label: {
                        Image(systemName: "plus")
                    }
                }
            }
            .sheet(isPresented: $showingRecordingView) {
                RecordingView()
            }
            .sheet(item: $selectedVoiceClone) { voiceClone in
                VoiceCloneDetailView(voiceClone: voiceClone)
            }
        }
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Spacer()
            
            Image(systemName: "waveform.circle")
                .font(.system(size: 64))
                .foregroundStyle(.blue.gradient)
            
            VStack(spacing: 8) {
                Text("还没有语音克隆")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("录制你的声音来创建个性化语音克隆")
                    .font(.subheadline)
                    .foregroundStyle(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Button {
                showingRecordingView = true
            } label: {
                Label("开始录音", systemImage: "mic.fill")
                    .font(.headline)
                    .foregroundStyle(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(.red.gradient)
                    )
            }
            .buttonStyle(.plain)
            
            Spacer()
        }
        .frame(maxWidth: .infinity)
        .padding()
    }
    
    private var voiceCloneListContent: some View {
        List {
            ForEach(appViewModel.voiceClones.sorted { $0.createdAt > $1.createdAt }) { voiceClone in
                Button {
                    selectedVoiceClone = voiceClone
                } label: {
                    VoiceCloneRowView(voiceClone: voiceClone)
                }
                .buttonStyle(.plain)
                .listRowInsets(EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 16))
                .listRowSeparator(.hidden)
            }
            .onDelete(perform: deleteVoiceClones)
        }
        .listStyle(.plain)
        .refreshable {
            // 刷新逻辑
        }
    }
    
    private func deleteVoiceClones(offsets: IndexSet) {
        for index in offsets {
            let voiceClone = appViewModel.voiceClones.sorted { $0.createdAt > $1.createdAt }[index]
            appViewModel.deleteVoiceClone(voiceClone)
        }
    }
}

/// 语音克隆行视图
struct VoiceCloneRowView: View {
    let voiceClone: VoiceClone
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题和状态
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(voiceClone.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .lineLimit(1)
                    
                    Text(voiceClone.language.name)
                        .font(.subheadline)
                        .foregroundStyle(.secondary)
                }
                
                Spacer()
                
                VoiceCloneStatusBadge(status: voiceClone.status)
            }
            
            // 录音信息
            HStack(spacing: 16) {
                Label(voiceClone.audioRecording.formattedDuration, systemImage: "clock")
                    .font(.caption)
                    .foregroundStyle(.secondary)
                
                Label(voiceClone.audioRecording.formattedFileSize, systemImage: "doc.circle")
                    .font(.caption)
                    .foregroundStyle(.secondary)
                
                Spacer()
            }
            
            // 进度条（如果正在处理）
            if voiceClone.status == .processing {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text("处理中...")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                        
                        Spacer()
                        
                        Text("\(Int(voiceClone.progress * 100))%")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                    }
                    
                    ProgressView(value: voiceClone.progress)
                        .progressViewStyle(.linear)
                        .tint(.blue)
                }
            }
            
            // 底部信息
            HStack {
                Text(voiceClone.formattedCreatedAt)
                    .font(.caption)
                    .foregroundStyle(.secondary)
                
                Spacer()
                
                if voiceClone.canUsedForSynthesis {
                    Label("可用于合成", systemImage: "checkmark.circle.fill")
                        .font(.caption)
                        .foregroundStyle(.green)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
                .stroke(.separator, lineWidth: 0.5)
        )
    }
}

/// 语音克隆状态徽章
struct VoiceCloneStatusBadge: View {
    let status: VoiceCloneStatus
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: status.icon)
                .font(.caption2)
            
            Text(status.displayName)
                .font(.caption2)
                .fontWeight(.medium)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(status.color.opacity(0.2))
        )
        .foregroundStyle(status.color)
    }
}

/// 语音克隆详情视图
struct VoiceCloneDetailView: View {
    let voiceClone: VoiceClone
    @Environment(\.dismiss) private var dismiss
    @State private var isPlaying = false
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 24) {
                    // 语音克隆信息
                    voiceCloneInfoSection
                    
                    // 录音播放区域
                    audioPlaybackSection
                    
                    // 状态和进度
                    statusSection
                    
                    // 操作按钮
                    actionButtonsSection
                }
                .padding()
            }
            .navigationTitle(voiceClone.name)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private var voiceCloneInfoSection: some View {
        VStack(spacing: 16) {
            // 语音克隆图标
            Image(systemName: "waveform.circle.fill")
                .font(.system(size: 64))
                .foregroundStyle(.blue.gradient)
            
            VStack(spacing: 8) {
                Text(voiceClone.name)
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text(voiceClone.language.name)
                    .font(.subheadline)
                    .foregroundStyle(.secondary)
            }
            
            VoiceCloneStatusBadge(status: voiceClone.status)
        }
    }
    
    private var audioPlaybackSection: some View {
        VStack(spacing: 16) {
            Text("原始录音")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                // 波形显示区域（简化版）
                RoundedRectangle(cornerRadius: 8)
                    .fill(.ultraThinMaterial)
                    .frame(height: 60)
                    .overlay {
                        HStack(spacing: 2) {
                            ForEach(0..<20, id: \.self) { _ in
                                RoundedRectangle(cornerRadius: 1)
                                    .fill(.blue.opacity(0.6))
                                    .frame(width: 3, height: CGFloat.random(in: 10...50))
                            }
                        }
                    }
                
                // 播放控制
                HStack(spacing: 20) {
                    Button {
                        // 播放/暂停逻辑
                        isPlaying.toggle()
                    } label: {
                        Image(systemName: isPlaying ? "pause.circle.fill" : "play.circle.fill")
                            .font(.system(size: 44))
                            .foregroundStyle(.blue)
                    }
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("时长: \(voiceClone.audioRecording.formattedDuration)")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                        
                        Text("大小: \(voiceClone.audioRecording.formattedFileSize)")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                    }
                    
                    Spacer()
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
            )
        }
    }
    
    private var statusSection: some View {
        VStack(spacing: 16) {
            Text("克隆状态")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                HStack {
                    Text("状态")
                        .foregroundStyle(.secondary)
                    
                    Spacer()
                    
                    Text(voiceClone.status.displayName)
                        .fontWeight(.medium)
                        .foregroundStyle(voiceClone.status.color)
                }
                
                if voiceClone.status == .processing {
                    VStack(spacing: 8) {
                        HStack {
                            Text("进度")
                                .foregroundStyle(.secondary)
                            
                            Spacer()
                            
                            Text("\(Int(voiceClone.progress * 100))%")
                                .fontWeight(.medium)
                        }
                        
                        ProgressView(value: voiceClone.progress)
                            .progressViewStyle(.linear)
                            .tint(.blue)
                    }
                }
                
                HStack {
                    Text("创建时间")
                        .foregroundStyle(.secondary)
                    
                    Spacer()
                    
                    Text(voiceClone.formattedCreatedAt)
                        .fontWeight(.medium)
                }
                
                if let cloneId = voiceClone.cloneId {
                    HStack {
                        Text("克隆ID")
                            .foregroundStyle(.secondary)
                        
                        Spacer()
                        
                        Text(cloneId)
                            .font(.caption)
                            .fontFamily(.monospaced)
                            .foregroundStyle(.secondary)
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
            )
        }
    }
    
    private var actionButtonsSection: some View {
        VStack(spacing: 12) {
            if voiceClone.canUsedForSynthesis {
                Button {
                    // 使用此声音生成故事
                } label: {
                    Label("使用此声音生成故事", systemImage: "wand.and.stars")
                        .font(.headline)
                        .foregroundStyle(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(.blue.gradient)
                        )
                }
                .buttonStyle(.plain)
            }
            
            Button(role: .destructive) {
                // 删除语音克隆
            } label: {
                Label("删除语音克隆", systemImage: "trash")
                    .font(.headline)
                    .foregroundStyle(.red)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(.red.opacity(0.1))
                    )
            }
            .buttonStyle(.plain)
        }
    }
}

#Preview {
    VoiceCloneListView()
        .environment(AppViewModel())
}
