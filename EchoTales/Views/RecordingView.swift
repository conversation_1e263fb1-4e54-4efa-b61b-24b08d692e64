//
//  RecordingView.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import SwiftUI

/// 录音界面视图
struct RecordingView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(AppViewModel.self) private var appViewModel
    @StateObject private var recordingViewModel: RecordingViewModel

    init() {
        self._recordingViewModel = StateObject(wrappedValue: RecordingViewModel(
            audioService: AudioRecordingService(),
            appViewModel: AppViewModel()
        ))
    }
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 32) {
                // 顶部信息
                headerSection
                
                // 录音控制区域
                recordingControlSection
                
                // 录音状态和信息
                recordingInfoSection
                
                // 语言选择
                VStack(alignment: .leading, spacing: 12) {
                    Text("选择语言")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    LazyVGrid(columns: [
                        GridItem(.flexible()),
                        GridItem(.flexible())
                    ], spacing: 12) {
                        ForEach(Language.supportedLanguages, id: \.code) { language in
                            Button {
                                recordingViewModel.selectedLanguage = language
                            } label: {
                                HStack {
                                    Text(language.name)
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                    
                                    Spacer()
                                    
                                    if recordingViewModel.selectedLanguage.code == language.code {
                                        Image(systemName: "checkmark.circle.fill")
                                            .foregroundStyle(.blue)
                                    }
                                }
                                .padding()
                                .background {
                                    let isSelected = recordingViewModel.selectedLanguage.code == language.code
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(isSelected ? .blue.opacity(0.1) : Color.clear)
                                        .stroke(isSelected ? .blue : .clear, lineWidth: 1)
                                        .background(isSelected ? Color.clear : .ultraThinMaterial, in: RoundedRectangle(cornerRadius: 12))
                                }
                                .foregroundStyle(.primary)
                            }
                            .buttonStyle(.plain)
                        }
                    }
                }
                
                Spacer()
                
                // 底部按钮
                bottomButtonsSection
            }
            .padding()
            .navigationTitle("录制声音")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
            .alert("权限请求", isPresented: $recordingViewModel.showPermissionAlert) {
                Button("设置") {
                    // 打开设置
                }
                Button("取消", role: .cancel) { }
            } message: {
                Text("需要麦克风权限来录制音频")
            }
            .alert("错误", isPresented: $recordingViewModel.showError) {
                Button("确定", role: .cancel) { }
            } message: {
                Text(recordingViewModel.errorMessage ?? "未知错误")
            }
        }
    }
    
    // MARK: - 视图组件
    
    private var headerSection: some View {
        VStack(spacing: 8) {
            Image(systemName: "waveform")
                .font(.system(size: 48))
                .foregroundStyle(.blue)
            
            Text("录制您的声音")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("录制一段清晰的语音来创建您的专属声音克隆")
                .font(.subheadline)
                .foregroundStyle(.secondary)
                .multilineTextAlignment(.center)
        }
    }
    
    private var recordingControlSection: some View {
        VStack(spacing: 24) {
            // 波形显示
            WaveformView(isRecording: recordingViewModel.isRecording)
                .frame(height: 80)
            
            // 录音按钮
            Button {
                Task {
                    if recordingViewModel.isRecording {
                        await recordingViewModel.stopRecording()
                    } else {
                        await recordingViewModel.startRecording()
                    }
                }
            } label: {
                ZStack {
                    Circle()
                        .fill(recordingViewModel.isRecording ? .red : .blue)
                        .frame(width: 80, height: 80)
                    
                    if recordingViewModel.isRecording {
                        RoundedRectangle(cornerRadius: 4)
                            .fill(.white)
                            .frame(width: 24, height: 24)
                    } else {
                        Image(systemName: "mic.fill")
                            .font(.title)
                            .foregroundStyle(.white)
                    }
                }
            }
            .scaleEffect(recordingViewModel.isRecording ? 1.1 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: recordingViewModel.isRecording)
        }
    }
    
    private var recordingInfoSection: some View {
        VStack(spacing: 16) {
            if recordingViewModel.isRecording {
                VStack(spacing: 8) {
                    Text("录音中...")
                        .font(.headline)
                        .foregroundStyle(.red)
                    
                    Text(recordingViewModel.formattedDuration)
                        .font(.title)
                        .fontWeight(.bold)
                        .fontDesign(.monospaced)
                }
            } else if recordingViewModel.hasRecording {
                VStack(spacing: 12) {
                    Text("录音完成")
                        .font(.headline)
                        .foregroundStyle(.green)
                    
                    HStack(spacing: 16) {
                        // 播放按钮
                        Button {
                            Task {
                                await recordingViewModel.togglePlayback()
                            }
                        } label: {
                            Image(systemName: recordingViewModel.isPlaying ? "pause.circle.fill" : "play.circle.fill")
                                .font(.title)
                                .foregroundStyle(.blue)
                        }
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text("时长: \(recordingViewModel.formattedDuration)")
                                .font(.subheadline)
                            
                            if let recording = recordingViewModel.currentRecording {
                                Text("大小: \(recording.formattedFileSize)")
                                    .font(.caption)
                                    .foregroundStyle(.secondary)
                            }
                        }
                    }
                }
            }
        }
    }
    
    private var bottomButtonsSection: some View {
        VStack(spacing: 12) {
            if recordingViewModel.hasRecording {
                Button {
                    Task {
                        await recordingViewModel.createVoiceClone()
                        if recordingViewModel.voiceCloneCreated {
                            dismiss()
                        }
                    }
                } label: {
                    HStack {
                        if recordingViewModel.isCreatingVoiceClone {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                        
                        Text(recordingViewModel.isCreatingVoiceClone ? "创建中..." : "创建语音克隆")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .foregroundStyle(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(.blue.gradient)
                    )
                }
                .disabled(recordingViewModel.isCreatingVoiceClone)
                .buttonStyle(.plain)
                
                Button {
                    recordingViewModel.deleteRecording()
                } label: {
                    Text("重新录制")
                        .font(.headline)
                        .foregroundStyle(.red)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(.red.opacity(0.1))
                        )
                }
                .buttonStyle(.plain)
            }
        }
    }
}

/// 波形显示视图
struct WaveformView: View {
    let isRecording: Bool
    @State private var animationOffset: CGFloat = 0
    
    var body: some View {
        HStack(spacing: 4) {
            ForEach(0..<20, id: \.self) { index in
                RoundedRectangle(cornerRadius: 2)
                    .fill(.blue.opacity(0.7))
                    .frame(width: 3, height: waveHeight(for: index))
                    .animation(
                        .easeInOut(duration: 0.5)
                        .repeatForever(autoreverses: true)
                        .delay(Double(index) * 0.1),
                        value: isRecording
                    )
            }
        }
        .onAppear {
            if isRecording {
                withAnimation(.linear(duration: 2).repeatForever(autoreverses: false)) {
                    animationOffset = 1
                }
            }
        }
    }
    
    private func waveHeight(for index: Int) -> CGFloat {
        if isRecording {
            return CGFloat.random(in: 10...60)
        } else {
            return 4
        }
    }
}

#Preview {
    RecordingView()
}
