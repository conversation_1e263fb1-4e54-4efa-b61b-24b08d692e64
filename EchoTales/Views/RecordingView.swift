//
//  RecordingView.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import SwiftUI

/// 录音界面视图
struct RecordingView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(AppViewModel.self) private var appViewModel
    @StateObject private var recordingViewModel: RecordingViewModel

    init() {
        self._recordingViewModel = StateObject(wrappedValue: RecordingViewModel(
            audioService: AudioRecordingService(),
            appViewModel: AppViewModel()
        ))
    }
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 32) {
                // 顶部信息
                headerSection
                
                // 录音控制区域
                recordingControlSection
                
                // 录音状态和信息
                recordingInfoSection
                
                // 语言选择
                languageSelectionSection
                
                Spacer()
                
                // 底部按钮
                bottomButtonsSection
            }
            .padding()
            .navigationTitle("录制声音")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
            .alert("录音权限", isPresented: $recordingViewModel.showPermissionAlert) {
                Button("设置") {
                    // 打开设置
                }
                Button("取消", role: .cancel) { }
            } message: {
                Text("需要麦克风权限来录制声音，请在设置中允许访问。")
            }
            .alert("错误", isPresented: $recordingViewModel.showError) {
                Button("确定", role: .cancel) { }
            } message: {
                Text(recordingViewModel.errorMessage ?? "")
            }
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "waveform.circle.fill")
                .font(.system(size: 80))
                .foregroundStyle(.red.gradient)
            
            VStack(spacing: 8) {
                Text("录制你的声音")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("录制30秒以上的清晰语音以获得最佳效果")
                    .font(.subheadline)
                    .foregroundStyle(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
    }
    
    private var recordingControlSection: some View {
        VStack(spacing: 24) {
            // 波形显示
            if recordingViewModel.isRecording {
                WaveformView(isRecording: recordingViewModel.isRecording)
                    .frame(height: 80)
            } else {
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.clear)
                    .frame(height: 80)
                    .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 12))
                    .overlay {
                        Text("按住录音按钮开始录制")
                            .font(.subheadline)
                            .foregroundStyle(.secondary)
                    }
            }
            
            // 录音按钮
            Button {
                if recordingViewModel.isRecording {
                    recordingViewModel.stopRecording()
                } else {
                    recordingViewModel.startRecording()
                }
            } label: {
                ZStack {
                    Circle()
                        .fill(recordingViewModel.isRecording ? .red : .red.opacity(0.1))
                        .frame(width: 100, height: 100)
                    
                    Circle()
                        .stroke(.red, lineWidth: 4)
                        .frame(width: 100, height: 100)
                    
                    Image(systemName: recordingViewModel.isRecording ? "stop.fill" : "mic.fill")
                        .font(.system(size: 32))
                        .foregroundStyle(recordingViewModel.isRecording ? .white : .red)
                }
            }
            .scaleEffect(recordingViewModel.isRecording ? 1.1 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: recordingViewModel.isRecording)
        }
    }
    
    private var recordingInfoSection: some View {
        VStack(spacing: 12) {
            if recordingViewModel.isRecording {
                HStack {
                    Image(systemName: "record.circle.fill")
                        .foregroundStyle(.red)
                        .font(.caption)
                    
                    Text("录音中...")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                
                Text(recordingViewModel.formattedDuration)
                    .font(.title)
                    .fontWeight(.bold)
                    .fontFamily(.monospaced)
            } else if recordingViewModel.hasRecording {
                VStack(spacing: 8) {
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundStyle(.green)
                        
                        Text("录音完成")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    
                    Text("时长: \(recordingViewModel.formattedDuration)")
                        .font(.subheadline)
                        .foregroundStyle(.secondary)
                    
                    // 播放按钮
                    Button {
                        recordingViewModel.togglePlayback()
                    } label: {
                        HStack {
                            Image(systemName: recordingViewModel.isPlaying ? "pause.circle.fill" : "play.circle.fill")
                            Text(recordingViewModel.isPlaying ? "暂停" : "播放")
                        }
                        .font(.subheadline)
                        .foregroundStyle(.blue)
                    }
                }
            }
        }
    }
    
    private var languageSelectionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("选择语言")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(Language.supportedLanguages, id: \.code) { language in
                    Button {
                        recordingViewModel.selectedLanguage = language
                    } label: {
                        HStack {
                            Text(language.name)
                                .font(.subheadline)
                                .fontWeight(.medium)
                            
                            Spacer()
                            
                            if recordingViewModel.selectedLanguage.code == language.code {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundStyle(.blue)
                            }
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(recordingViewModel.selectedLanguage.code == language.code ? .blue.opacity(0.1) : Color.clear)
                                .stroke(recordingViewModel.selectedLanguage.code == language.code ? .blue : .clear, lineWidth: 1)
                                .background(recordingViewModel.selectedLanguage.code == language.code ? Color.clear : .ultraThinMaterial, in: RoundedRectangle(cornerRadius: 12))
                        )
                        .foregroundStyle(.primary)
                    }
                    .buttonStyle(.plain)
                }
            }
        }
    }
    
    private var bottomButtonsSection: some View {
        VStack(spacing: 12) {
            if recordingViewModel.hasRecording {
                Button {
                    Task {
                        await recordingViewModel.createVoiceClone()
                        if recordingViewModel.voiceCloneCreated {
                            dismiss()
                        }
                    }
                } label: {
                    HStack {
                        if recordingViewModel.isCreatingVoiceClone {
                            ProgressView()
                                .scaleEffect(0.8)
                        }
                        
                        Text(recordingViewModel.isCreatingVoiceClone ? "创建中..." : "创建语音克隆")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .foregroundStyle(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(.blue.gradient)
                    )
                }
                .disabled(recordingViewModel.isCreatingVoiceClone)
                .buttonStyle(.plain)
                
                Button {
                    recordingViewModel.deleteRecording()
                } label: {
                    Text("重新录制")
                        .font(.headline)
                        .foregroundStyle(.red)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(.red.opacity(0.1))
                        )
                }
                .buttonStyle(.plain)
            }
        }
    }
}

/// 波形显示视图
struct WaveformView: View {
    let isRecording: Bool
    @State private var animationOffset: CGFloat = 0
    
    var body: some View {
        HStack(spacing: 2) {
            ForEach(0..<40, id: \.self) { index in
                RoundedRectangle(cornerRadius: 1)
                    .fill(.red.opacity(0.8))
                    .frame(width: 3)
                    .frame(height: isRecording ? CGFloat.random(in: 10...60) : 20)
                    .animation(
                        .easeInOut(duration: 0.3)
                        .repeatForever(autoreverses: true)
                        .delay(Double(index) * 0.05),
                        value: isRecording
                    )
            }
        }
        .onAppear {
            if isRecording {
                withAnimation(.linear(duration: 2).repeatForever(autoreverses: false)) {
                    animationOffset = 1
                }
            }
        }
    }
}

#Preview {
    RecordingView()
        .environment(AppViewModel())
}
