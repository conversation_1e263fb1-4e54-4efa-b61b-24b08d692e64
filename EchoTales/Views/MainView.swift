//
//  MainView.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import SwiftUI

/// 主界面视图
struct MainView: View {
    @Environment(AppViewModel.self) private var appViewModel
    @State private var selectedTab: MainTab = .home
    
    var body: some View {
        TabView(selection: $selectedTab) {
            HomeView()
                .tabItem {
                    Image(systemName: "house.fill")
                    Text("首页")
                }
                .tag(MainTab.home)
            
            StoryListView()
                .tabItem {
                    Image(systemName: "book.fill")
                    Text("故事")
                }
                .tag(MainTab.stories)
            
            VoiceCloneListView()
                .tabItem {
                    Image(systemName: "waveform")
                    Text("声音")
                }
                .tag(MainTab.voices)
            
            SettingsView()
                .tabItem {
                    Image(systemName: "gearshape.fill")
                    Text("设置")
                }
                .tag(MainTab.settings)
        }
        .tint(.primary)
        .onAppear {
            setupTabBarAppearance()
        }
    }
    
    private func setupTabBarAppearance() {
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor.systemBackground
        
        UITabBar.appearance().standardAppearance = appearance
        UITabBar.appearance().scrollEdgeAppearance = appearance
    }
}

/// 主界面标签页枚举
enum MainTab: String, CaseIterable {
    case home = "home"
    case stories = "stories"
    case voices = "voices"
    case settings = "settings"
    
    var title: String {
        switch self {
        case .home:
            return "首页"
        case .stories:
            return "故事"
        case .voices:
            return "声音"
        case .settings:
            return "设置"
        }
    }
    
    var icon: String {
        switch self {
        case .home:
            return "house.fill"
        case .stories:
            return "book.fill"
        case .voices:
            return "waveform"
        case .settings:
            return "gearshape.fill"
        }
    }
}

/// 首页视图
struct HomeView: View {
    @Environment(AppViewModel.self) private var appViewModel
    @State private var showingRecordingView = false
    @State private var showingStoryGenerationView = false
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 24) {
                    // 欢迎区域
                    welcomeSection
                    
                    // 快速操作区域
                    quickActionsSection
                    
                    // 最近故事区域
                    recentStoriesSection
                    
                    // 语音克隆状态区域
                    voiceCloneStatusSection
                }
                .padding()
            }
            .navigationTitle("EchoTales")
            .navigationBarTitleDisplayMode(.large)
            .sheet(isPresented: $showingRecordingView) {
                RecordingView()
            }
            .sheet(isPresented: $showingStoryGenerationView) {
                StoryGenerationView()
            }
        }
    }
    
    private var welcomeSection: some View {
        VStack(spacing: 12) {
            Image(systemName: "sparkles")
                .font(.system(size: 48))
                .foregroundStyle(.blue.gradient)
            
            Text("欢迎来到 EchoTales")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("用你的声音讲述精彩故事")
                .font(.subheadline)
                .foregroundStyle(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.vertical)
    }
    
    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("快速开始")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                QuickActionCard(
                    title: "录制声音",
                    subtitle: "创建语音克隆",
                    icon: "mic.fill",
                    color: .red,
                    action: { showingRecordingView = true }
                )
                
                QuickActionCard(
                    title: "生成故事",
                    subtitle: "AI创作童话",
                    icon: "wand.and.stars",
                    color: .purple,
                    action: { showingStoryGenerationView = true }
                )
            }
        }
    }
    
    private var recentStoriesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("最近故事")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                NavigationLink("查看全部") {
                    StoryListView()
                }
                .font(.subheadline)
                .foregroundStyle(.blue)
            }
            
            if appViewModel.stories.isEmpty {
                EmptyStateView(
                    icon: "book",
                    title: "还没有故事",
                    subtitle: "开始创作你的第一个故事吧！"
                )
                .frame(height: 120)
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(Array(appViewModel.stories.prefix(3))) { story in
                        StoryRowView(story: story)
                    }
                }
            }
        }
    }
    
    private var voiceCloneStatusSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("语音克隆")
                .font(.headline)
                .fontWeight(.semibold)
            
            if appViewModel.voiceClones.isEmpty {
                EmptyStateView(
                    icon: "waveform",
                    title: "还没有语音克隆",
                    subtitle: "录制你的声音来创建个性化语音"
                )
                .frame(height: 120)
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(Array(appViewModel.voiceClones.prefix(2))) { voiceClone in
                        VoiceCloneRowView(voiceClone: voiceClone)
                    }
                }
            }
        }
    }
}

/// 快速操作卡片
struct QuickActionCard: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.system(size: 32))
                    .foregroundStyle(color.gradient)
                
                VStack(spacing: 4) {
                    Text(title)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundStyle(.primary)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .stroke(color.opacity(0.3), lineWidth: 1)
            )
        }
        .buttonStyle(.plain)
    }
}

/// 空状态视图
struct EmptyStateView: View {
    let icon: String
    let title: String
    let subtitle: String
    
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 32))
                .foregroundStyle(.secondary)
            
            VStack(spacing: 4) {
                Text(title)
                    .font(.headline)
                    .foregroundStyle(.primary)
                
                Text(subtitle)
                    .font(.subheadline)
                    .foregroundStyle(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
}

#Preview {
    MainView()
        .environment(AppViewModel())
}
