//
//  MainView.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import SwiftUI

/// 主界面视图
struct MainView: View {
    @Environment(AppViewModel.self) private var appViewModel
    @State private var selectedTab: MainTab = .home
    
    var body: some View {
        TabView(selection: $selectedTab) {
            HomeView()
                .tabItem {
                    Image(systemName: "house.fill")
                    Text("首页")
                }
                .tag(MainTab.home)

            VoiceCloneListView()
                .tabItem {
                    Image(systemName: "waveform")
                    Text("声音")
                }
                .tag(MainTab.voices)

            StoryListView()
                .tabItem {
                    Image(systemName: "book.fill")
                    Text("故事")
                }
                .tag(MainTab.stories)

            SettingsView()
                .tabItem {
                    Image(systemName: "gearshape.fill")
                    Text("设置")
                }
                .tag(MainTab.settings)
        }
        .tint(.primary)
        .onAppear {
            setupTabBarAppearance()
        }
    }
    
    private func setupTabBarAppearance() {
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor.systemBackground
        
        UITabBar.appearance().standardAppearance = appearance
        UITabBar.appearance().scrollEdgeAppearance = appearance
    }
}

/// 主界面标签页枚举
enum MainTab: String, CaseIterable {
    case home = "home"
    case stories = "stories"
    case voices = "voices"
    case settings = "settings"
    
    var title: String {
        switch self {
        case .home:
            return "首页"
        case .stories:
            return "故事"
        case .voices:
            return "声音"
        case .settings:
            return "设置"
        }
    }
    
    var icon: String {
        switch self {
        case .home:
            return "house.fill"
        case .stories:
            return "book.fill"
        case .voices:
            return "waveform"
        case .settings:
            return "gearshape.fill"
        }
    }
}



/// 快速操作卡片
struct QuickActionCard: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.system(size: 32))
                    .foregroundStyle(color.gradient)
                
                VStack(spacing: 4) {
                    Text(title)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundStyle(.primary)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.clear)
                    .stroke(color.opacity(0.3), lineWidth: 1)
                    .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 16))
            )
        }
        .buttonStyle(.plain)
    }
}

/// 空状态视图
struct EmptyStateView: View {
    let icon: String
    let title: String
    let subtitle: String
    
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 32))
                .foregroundStyle(.secondary)
            
            VStack(spacing: 4) {
                Text(title)
                    .font(.headline)
                    .foregroundStyle(.primary)
                
                Text(subtitle)
                    .font(.subheadline)
                    .foregroundStyle(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 12))
    }
}

#Preview {
    MainView()
        .environment(AppViewModel())
}
