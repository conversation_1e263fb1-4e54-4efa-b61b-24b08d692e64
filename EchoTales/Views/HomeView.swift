//
//  HomeView.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import SwiftUI



/// 首页视图
struct HomeView: View {
    @Environment(AppViewModel.self) private var appViewModel

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 欢迎标题
                    welcomeSection

                    // 快速开始
                    quickStartSection

                    // 最近活动
                    recentActivitySection
                }
                .padding()
                .padding(.top, 20)
            }
            .navigationTitle("EchoTales")
            .navigationBarTitleDisplayMode(.large)
            .refreshable {
                await refreshData()
            }
        }
    }
    
    // MARK: - View Components
    
    private var welcomeSection: some View {
        VStack(spacing: 12) {
            Image(systemName: "waveform.and.mic")
                .font(.system(size: 50))
                .foregroundStyle(Color.blue.gradient)
                .symbolEffect(.pulse)

            Text("EchoTales")
                .font(.title2)
                .fontWeight(.bold)

            Text("用您的声音讲故事")
                .font(.subheadline)
                .foregroundStyle(.secondary)
        }
    }
    
    private var quickStartSection: some View {
        VStack(spacing: 16) {
            Text("快速开始")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                QuickActionCard(
                    title: "录制声音",
                    subtitle: "创建声音克隆",
                    icon: "mic.fill",
                    color: .blue
                ) {
                    // 跳转到录音页面
                }

                QuickActionCard(
                    title: "生成故事",
                    subtitle: "AI创作故事",
                    icon: "book.fill",
                    color: .green
                ) {
                    // 跳转到故事页面
                }
            }
        }
    }

    private var recentActivitySection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("最近活动")
                    .font(.headline)
                Spacer()
                if !appViewModel.stories.isEmpty || !appViewModel.voiceClones.isEmpty {
                    Button("查看全部") {
                        // 可以添加查看全部的逻辑
                    }
                    .font(.caption)
                    .foregroundStyle(.blue)
                }
            }

            if appViewModel.stories.isEmpty && appViewModel.voiceClones.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "tray")
                        .font(.system(size: 30))
                        .foregroundStyle(.secondary)
                    Text("暂无活动")
                        .font(.subheadline)
                        .foregroundStyle(.secondary)
                    Text("开始录制声音或生成故事吧！")
                        .font(.caption)
                        .foregroundStyle(.tertiary)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 20)
            } else {
                VStack(spacing: 12) {
                    if !appViewModel.voiceClones.isEmpty {
                        HStack {
                            Image(systemName: "waveform")
                                .foregroundStyle(.blue)
                            Text("声音克隆: \(appViewModel.voiceClones.count) 个")
                                .font(.subheadline)
                            Spacer()
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color.blue.opacity(0.1))
                        .clipShape(RoundedRectangle(cornerRadius: 8))
                    }

                    if !appViewModel.stories.isEmpty {
                        HStack {
                            Image(systemName: "book")
                                .foregroundStyle(.green)
                            Text("故事: \(appViewModel.stories.count) 个")
                                .font(.subheadline)
                            Spacer()
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(Color.green.opacity(0.1))
                        .clipShape(RoundedRectangle(cornerRadius: 8))
                    }
                }
            }
        }
    }
    

    
    // MARK: - Methods

    private func refreshData() async {
        // 刷新数据的逻辑
        try? await Task.sleep(nanoseconds: 1_000_000_000)
    }
}

// MARK: - Supporting Views

#Preview {
    HomeView()
        .environment(AppViewModel())
}
