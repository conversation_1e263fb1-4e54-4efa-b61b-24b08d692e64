//
//  HomeView.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import SwiftUI

/// 用户角色
enum UserRole: String, CaseIterable {
    case father = "爸爸"
    case mother = "妈妈"
    
    var icon: String {
        switch self {
        case .father: return "figure.dress.line.vertical.figure"
        case .mother: return "figure.dress.line.vertical.figure"
        }
    }
    
    var description: String {
        switch self {
        case .father: return "为孩子录制爸爸的声音"
        case .mother: return "为孩子录制妈妈的声音"
        }
    }
}

/// 首页视图
struct HomeView: View {
    @Environment(AppViewModel.self) private var appViewModel
    @State private var selectedRole: UserRole?
    @State private var showingRecording = false
    @State private var showingNetworkAlert = false
    @State private var showingPermissionAlert = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // 欢迎标题
                welcomeSection
                
                // 角色选择
                roleSelectionSection
                
                // 开始录音按钮
                startRecordingSection
                
                Spacer()
                
                // 应用介绍
                appIntroSection
            }
            .padding()
            .navigationTitle("EchoTales")
            .navigationBarTitleDisplayMode(.large)
            .onAppear {
                checkNetworkAndPermissions()
            }
            .alert("网络连接", isPresented: $showingNetworkAlert) {
                Button("重试") {
                    checkNetworkAndPermissions()
                }
                Button("取消", role: .cancel) { }
            } message: {
                Text("请检查网络连接，确保可以正常使用语音克隆功能。")
            }
            .alert("麦克风权限", isPresented: $showingPermissionAlert) {
                Button("去设置") {
                    if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                        UIApplication.shared.open(settingsUrl)
                    }
                }
                Button("取消", role: .cancel) { }
            } message: {
                Text("请在设置中开启麦克风权限，以便录制您的声音。")
            }
            .sheet(isPresented: $showingRecording) {
                RecordingView()
            }
        }
    }
    
    // MARK: - View Components
    
    private var welcomeSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "waveform.circle.fill")
                .font(.system(size: 80))
                .foregroundStyle(Color.blue.gradient)
            
            Text("欢迎使用 EchoTales")
                .font(.title)
                .fontWeight(.bold)
            
            Text("用您的声音为孩子讲故事")
                .font(.headline)
                .foregroundStyle(.secondary)
                .multilineTextAlignment(.center)
        }
    }
    
    private var roleSelectionSection: some View {
        VStack(spacing: 16) {
            Text("请选择您的角色")
                .font(.headline)
                .foregroundStyle(.primary)
            
            HStack(spacing: 20) {
                ForEach(UserRole.allCases, id: \.self) { role in
                    RoleSelectionCard(
                        role: role,
                        isSelected: selectedRole == role
                    ) {
                        selectedRole = role
                        appViewModel.selectedUserRole = role.rawValue
                    }
                }
            }
        }
    }
    
    private var startRecordingSection: some View {
        VStack(spacing: 16) {
            Button {
                startRecordingFlow()
            } label: {
                HStack {
                    Image(systemName: "mic.fill")
                    Text("开始录音")
                        .fontWeight(.semibold)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue.gradient)
                .foregroundStyle(.white)
                .clipShape(RoundedRectangle(cornerRadius: 12))
            }
            .disabled(selectedRole == nil)
            
            if selectedRole == nil {
                Text("请先选择角色")
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }
        }
    }
    
    private var appIntroSection: some View {
        VStack(spacing: 12) {
            Text("如何使用")
                .font(.headline)
                .foregroundStyle(.primary)
            
            VStack(alignment: .leading, spacing: 8) {
                IntroStep(number: 1, text: "选择您的角色（爸爸或妈妈）")
                IntroStep(number: 2, text: "录制15秒语音进行声音克隆")
                IntroStep(number: 3, text: "生成或输入故事内容")
                IntroStep(number: 4, text: "用您的声音为孩子朗读")
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    // MARK: - Methods
    
    private func checkNetworkAndPermissions() {
        Task {
            // 检查网络连接
            let hasNetwork = await checkNetworkConnection()
            if !hasNetwork {
                await MainActor.run {
                    showingNetworkAlert = true
                }
                return
            }
            
            // 检查麦克风权限
            let audioService = AudioRecordingService()
            let hasPermission = await audioService.requestMicrophonePermission()
            if !hasPermission {
                await MainActor.run {
                    showingPermissionAlert = true
                }
            }
        }
    }
    
    private func checkNetworkConnection() async -> Bool {
        do {
            let url = URL(string: "https://www.apple.com")!
            let (_, response) = try await URLSession.shared.data(from: url)
            if let httpResponse = response as? HTTPURLResponse {
                return httpResponse.statusCode == 200
            }
            return false
        } catch {
            return false
        }
    }
    
    private func startRecordingFlow() {
        guard selectedRole != nil else { return }
        
        Task {
            // 再次检查网络和权限
            let hasNetwork = await checkNetworkConnection()
            let audioService = AudioRecordingService()
            let hasPermission = await audioService.requestMicrophonePermission()
            
            await MainActor.run {
                if !hasNetwork {
                    showingNetworkAlert = true
                } else if !hasPermission {
                    showingPermissionAlert = true
                } else {
                    showingRecording = true
                }
            }
        }
    }
}

// MARK: - Supporting Views

struct RoleSelectionCard: View {
    let role: UserRole
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                Image(systemName: role.icon)
                    .font(.system(size: 40))
                    .foregroundStyle(isSelected ? Color.white : Color.blue)
                
                Text(role.rawValue)
                    .font(.headline)
                    .foregroundStyle(isSelected ? .white : .primary)
                
                Text(role.description)
                    .font(.caption)
                    .foregroundStyle(isSelected ? .white.opacity(0.8) : .secondary)
                    .multilineTextAlignment(.center)
            }
            .frame(width: 140, height: 120)
            .padding()
            .background(isSelected ? AnyShapeStyle(Color.blue.gradient) : AnyShapeStyle(Color.gray.opacity(0.1)))
            .clipShape(RoundedRectangle(cornerRadius: 16))
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(.plain)
    }
}

struct IntroStep: View {
    let number: Int
    let text: String
    
    var body: some View {
        HStack(spacing: 12) {
            Text("\(number)")
                .font(.caption)
                .fontWeight(.bold)
                .foregroundStyle(.white)
                .frame(width: 20, height: 20)
                .background(Color.blue.gradient)
                .clipShape(Circle())
            
            Text(text)
                .font(.caption)
                .foregroundStyle(.primary)
            
            Spacer()
        }
    }
}

#Preview {
    HomeView()
        .environment(AppViewModel())
}
