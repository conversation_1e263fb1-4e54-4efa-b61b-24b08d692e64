//
//  StoryGenerationView.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import SwiftUI

/// 故事生成界面视图
struct StoryGenerationView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(AppViewModel.self) private var appViewModel
    @StateObject private var viewModel: StoryGenerationViewModel
    
    init() {
        self._viewModel = StateObject(wrappedValue: StoryGenerationViewModel(
            storyGenerationService: StoryGenerationService(),
            appViewModel: AppViewModel()
        ))
    }
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 24) {
                    // 顶部信息
                    headerSection
                    
                    // 主题输入
                    themeInputSection
                    
                    // 推荐主题
                    suggestedThemesSection
                    
                    // 语言选择
                    languageSelectionSection
                    
                    // 生成按钮
                    generateButtonSection
                    
                    // 生成进度
                    if viewModel.isGenerating {
                        generationProgressSection
                    }
                    
                    // 生成结果
                    if viewModel.hasStory {
                        storyResultSection
                    }
                }
                .padding()
            }
            .navigationTitle("生成故事")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
            .alert("错误", isPresented: $viewModel.showError) {
                Button("确定", role: .cancel) { }
            } message: {
                Text(viewModel.errorMessage ?? "")
            }
            .alert("成功", isPresented: $viewModel.showSuccess) {
                Button("确定", role: .cancel) { }
            } message: {
                Text(viewModel.successMessage)
            }
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "wand.and.stars")
                .font(.system(size: 80))
                .foregroundStyle(.purple.gradient)
            
            VStack(spacing: 8) {
                Text("AI 故事生成")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("输入主题，让AI为你创作精彩的童话故事")
                    .font(.subheadline)
                    .foregroundStyle(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
    }
    
    private var themeInputSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("故事主题")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(alignment: .leading, spacing: 8) {
                TextField("请输入故事主题...", text: $viewModel.theme, axis: .vertical)
                    .textFieldStyle(.roundedBorder)
                    .lineLimit(3...6)
                
                HStack {
                    Text("\(viewModel.themeCharacterCount)/100")
                        .font(.caption)
                        .foregroundColor(viewModel.isThemeValid ? .secondary : .red)
                    
                    Spacer()
                    
                    if !viewModel.isThemeValid && !viewModel.theme.isEmpty {
                        Text("主题长度应在2-100字符之间")
                            .font(.caption)
                            .foregroundStyle(.red)
                    }
                }
            }
        }
    }
    
    private var suggestedThemesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("推荐主题")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(viewModel.currentSuggestedThemes, id: \.self) { theme in
                    Button {
                        viewModel.selectSuggestedTheme(theme)
                    } label: {
                        Text(theme)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundStyle(.primary)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 12)
                            .frame(maxWidth: .infinity)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color.clear)
                                    .stroke(.separator, lineWidth: 0.5)
                                    .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 12))
                            )
                    }
                    .buttonStyle(.plain)
                }
            }
        }
    }
    
    private var languageSelectionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            languageSelectionHeader
            languageSelectionGrid
        }
    }

    private var languageSelectionHeader: some View {
        Text("选择语言")
            .font(.headline)
            .fontWeight(.semibold)
    }

    private var languageSelectionGrid: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 12) {
            ForEach(Language.supportedLanguages, id: \.code) { language in
                languageButton(for: language)
            }
        }
    }

    private func languageButton(for language: Language) -> some View {
        let isSelected = viewModel.selectedLanguage.code == language.code

        return Button {
            viewModel.selectLanguage(language)
        } label: {
            HStack {
                Text(language.name)
                    .font(.subheadline)
                    .fontWeight(.medium)

                Spacer()

                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundStyle(.blue)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? .blue.opacity(0.1) : Color.clear)
                    .stroke(isSelected ? .blue : .clear, lineWidth: 1)
            )
            .foregroundStyle(.primary)
        }
        .buttonStyle(.plain)
    }
    
    private var generateButtonSection: some View {
        Button {
            Task {
                await viewModel.generateStory()
            }
        } label: {
            HStack {
                if viewModel.isGenerating {
                    ProgressView()
                        .scaleEffect(0.8)
                }
                
                Text(viewModel.isGenerating ? "生成中..." : "生成故事")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            .foregroundStyle(.white)
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(viewModel.canGenerate ? Color.purple.gradient : Color.gray.gradient)
            )
        }
        .disabled(!viewModel.canGenerate)
        .buttonStyle(.plain)
    }
    
    private var generationProgressSection: some View {
        VStack(spacing: 16) {
            Text("正在生成故事...")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                ProgressView(value: viewModel.generationProgress)
                    .progressViewStyle(.linear)
                    .tint(.purple)
                
                HStack {
                    Text(viewModel.formattedProgress)
                        .font(.caption)
                        .foregroundStyle(.secondary)
                    
                    Spacer()
                    
                    Text("预计剩余: \(viewModel.estimatedTimeRemaining)")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
            }
            
            Button {
                Task {
                    await viewModel.cancelGeneration()
                }
            } label: {
                Text("取消生成")
                    .font(.subheadline)
                    .foregroundStyle(.red)
            }
        }
        .padding()
        .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 12))
    }
    
    private var storyResultSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("生成完成")
                .font(.headline)
                .fontWeight(.semibold)
            
            if let story = viewModel.currentStory {
                VStack(alignment: .leading, spacing: 12) {
                    // 故事标题
                    Text(story.title)
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    // 故事信息
                    HStack {
                        Label("\(viewModel.storyWordCount)字", systemImage: "textformat")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                        
                        Label("\(viewModel.estimatedReadingTime)分钟", systemImage: "clock")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                        
                        Spacer()
                    }
                    
                    // 故事内容预览
                    Text(story.content)
                        .font(.body)
                        .lineLimit(6)
                        .padding()
                        .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 12))
                    
                    // 操作按钮
                    HStack(spacing: 12) {
                        Button {
                            viewModel.saveStory()
                        } label: {
                            Label("保存", systemImage: "square.and.arrow.down")
                                .font(.subheadline)
                                .foregroundStyle(.blue)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(.blue.opacity(0.1))
                                )
                        }
                        .buttonStyle(.plain)
                        
                        Button {
                            if viewModel.shareStory() != nil {
                                // 分享故事
                            }
                        } label: {
                            Label("分享", systemImage: "square.and.arrow.up")
                                .font(.subheadline)
                                .foregroundStyle(.green)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(.green.opacity(0.1))
                                )
                        }
                        .buttonStyle(.plain)
                        
                        Spacer()
                        
                        Button {
                            Task {
                                await viewModel.regenerateStory()
                            }
                        } label: {
                            Label("重新生成", systemImage: "arrow.clockwise")
                                .font(.subheadline)
                                .foregroundStyle(.orange)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(.orange.opacity(0.1))
                                )
                        }
                        .buttonStyle(.plain)
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.clear)
                .stroke(.separator, lineWidth: 0.5)
                .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 12))
        )
    }
}

#Preview {
    StoryGenerationView()
        .environment(AppViewModel())
}
