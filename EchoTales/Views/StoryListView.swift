//
//  StoryListView.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import SwiftUI

/// 故事列表视图
struct StoryListView: View {
    @Environment(AppViewModel.self) private var appViewModel
    @State private var searchText = ""
    @State private var selectedLanguage: Language?
    @State private var showingStoryGenerationView = false
    
    var filteredStories: [Story] {
        var stories = appViewModel.stories
        
        // 按搜索文本过滤
        if !searchText.isEmpty {
            stories = stories.filter { story in
                story.title.localizedCaseInsensitiveContains(searchText) ||
                story.theme.localizedCaseInsensitiveContains(searchText) ||
                story.content.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        // 按语言过滤
        if let selectedLanguage = selectedLanguage {
            stories = stories.filter { $0.language.code == selectedLanguage.code }
        }
        
        return stories.sorted { $0.createdAt > $1.createdAt }
    }
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                // 搜索和过滤栏
                searchAndFilterSection
                
                // 故事列表
                if filteredStories.isEmpty {
                    emptyStateView
                } else {
                    storyListContent
                }
            }
            .navigationTitle("我的故事")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .topBarTrailing) {
                    Button {
                        showingStoryGenerationView = true
                    } label: {
                        Image(systemName: "plus")
                    }
                }
            }
            .sheet(isPresented: $showingStoryGenerationView) {
                StoryGenerationView()
            }
        }
    }
    
    private var searchAndFilterSection: some View {
        VStack(spacing: 12) {
            // 搜索栏
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundStyle(.secondary)
                
                TextField("搜索故事...", text: $searchText)
                    .textFieldStyle(.plain)
                
                if !searchText.isEmpty {
                    Button {
                        searchText = ""
                    } label: {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundStyle(.secondary)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial)
            )
            
            // 语言过滤器
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    FilterChip(
                        title: "全部",
                        isSelected: selectedLanguage == nil
                    ) {
                        selectedLanguage = nil
                    }
                    
                    ForEach(Language.supportedLanguages, id: \.code) { language in
                        FilterChip(
                            title: language.name,
                            isSelected: selectedLanguage?.code == language.code
                        ) {
                            selectedLanguage = language
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding()
        .background(.regularMaterial)
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Spacer()
            
            Image(systemName: "book.closed")
                .font(.system(size: 64))
                .foregroundStyle(.secondary)
            
            VStack(spacing: 8) {
                Text(searchText.isEmpty ? "还没有故事" : "没有找到匹配的故事")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text(searchText.isEmpty ? "开始创作你的第一个故事吧！" : "尝试调整搜索条件")
                    .font(.subheadline)
                    .foregroundStyle(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            if searchText.isEmpty {
                Button {
                    showingStoryGenerationView = true
                } label: {
                    Label("创作故事", systemImage: "plus")
                        .font(.headline)
                        .foregroundStyle(.white)
                        .padding(.horizontal, 24)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(.blue.gradient)
                        )
                }
                .buttonStyle(.plain)
            }
            
            Spacer()
        }
        .frame(maxWidth: .infinity)
    }
    
    private var storyListContent: some View {
        List {
            ForEach(filteredStories) { story in
                NavigationLink {
                    StoryDetailView(story: story)
                } label: {
                    StoryRowView(story: story)
                }
                .listRowInsets(EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 16))
                .listRowSeparator(.hidden)
            }
            .onDelete(perform: deleteStories)
        }
        .listStyle(.plain)
        .refreshable {
            // 刷新逻辑
        }
    }
    
    private func deleteStories(offsets: IndexSet) {
        for index in offsets {
            let story = filteredStories[index]
            appViewModel.deleteStory(story)
        }
    }
}

/// 故事行视图
struct StoryRowView: View {
    let story: Story
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题和状态
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(story.title)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .lineLimit(2)
                    
                    Text(story.theme)
                        .font(.subheadline)
                        .foregroundStyle(.secondary)
                        .lineLimit(1)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    StoryStatusBadge(status: story.status)
                    
                    Text(story.language.name)
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
            }
            
            // 内容预览
            if !story.content.isEmpty {
                Text(story.content)
                    .font(.body)
                    .lineLimit(3)
                    .foregroundStyle(.secondary)
            }
            
            // 底部信息
            HStack {
                Label(story.formattedCreatedAt, systemImage: "clock")
                    .font(.caption)
                    .foregroundStyle(.secondary)
                
                Spacer()
                
                if story.wordCount > 0 {
                    Label("\(story.wordCount)字", systemImage: "textformat")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
                
                if story.estimatedReadingTime > 0 {
                    Label("\(story.estimatedReadingTime)分钟", systemImage: "book")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
                .stroke(.separator, lineWidth: 0.5)
        )
    }
}

/// 故事状态徽章
struct StoryStatusBadge: View {
    let status: StoryStatus
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: status.icon)
                .font(.caption2)
            
            Text(status.displayName)
                .font(.caption2)
                .fontWeight(.medium)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(status.color.opacity(0.2))
        )
        .foregroundStyle(status.color)
    }
}

/// 过滤器芯片
struct FilterChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(isSelected ? .blue : Color.clear)
                )
                .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 20))
                .foregroundStyle(isSelected ? .white : .primary)
        }
        .buttonStyle(.plain)
    }
}

#Preview {
    StoryListView()
        .environment(AppViewModel())
}
