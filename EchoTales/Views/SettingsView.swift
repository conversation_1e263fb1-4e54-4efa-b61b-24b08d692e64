//
//  SettingsView.swift
//  EchoTales
//
//  Created by tim on 2025/7/6.
//

import SwiftUI

/// 设置视图
struct SettingsView: View {
    @Environment(AppViewModel.self) private var appViewModel
    @State private var showingAboutView = false
    @State private var showingPrivacyView = false
    @State private var showingHelpView = false
    
    var body: some View {
        NavigationStack {
            List {
                // 用户偏好设置
                preferencesSection
                
                // 存储和数据
                storageSection
                
                // 关于应用
                aboutSection
                
                // 帮助和支持
                supportSection
            }
            .navigationTitle("设置")
            .navigationBarTitleDisplayMode(.large)
            .sheet(isPresented: $showingAboutView) {
                AboutView()
            }
            .sheet(isPresented: $showingPrivacyView) {
                PrivacyView()
            }
            .sheet(isPresented: $showingHelpView) {
                HelpView()
            }
        }
    }
    
    private var preferencesSection: some View {
        Section("偏好设置") {
            // 默认语言设置
            HStack {
                Label("默认语言", systemImage: "globe")
                
                Spacer()
                
                Menu {
                    ForEach(Language.supportedLanguages, id: \.code) { language in
                        Button {
                            appViewModel.setSelectedLanguage(language)
                        } label: {
                            HStack {
                                Text(language.name)
                                if language.code == appViewModel.selectedLanguage.code {
                                    Image(systemName: "checkmark")
                                }
                            }
                        }
                    }
                } label: {
                    HStack {
                        Text(appViewModel.selectedLanguage.name)
                            .foregroundStyle(.secondary)
                        
                        Image(systemName: "chevron.up.chevron.down")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                    }
                }
            }
            
            // 音频质量设置
            HStack {
                Label("音频质量", systemImage: "waveform")
                
                Spacer()
                
                Menu {
                    Button("标准") { }
                    Button("高质量") { }
                    Button("最高质量") { }
                } label: {
                    HStack {
                        Text("标准")
                            .foregroundStyle(.secondary)
                        
                        Image(systemName: "chevron.up.chevron.down")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                    }
                }
            }
            
            // 自动播放设置
            HStack {
                Label("自动播放故事", systemImage: "play.circle")
                
                Spacer()
                
                Toggle("", isOn: .constant(true))
            }
        }
    }
    
    private var storageSection: some View {
        Section("存储和数据") {
            // 存储使用情况
            NavigationLink {
                StorageUsageView()
            } label: {
                HStack {
                    Label("存储使用情况", systemImage: "internaldrive")
                    
                    Spacer()
                    
                    Text("计算中...")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
            }
            
            // 清理缓存
            Button {
                // 清理缓存逻辑
            } label: {
                Label("清理缓存", systemImage: "trash.circle")
                    .foregroundStyle(.orange)
            }
            
            // 导出数据
            Button {
                // 导出数据逻辑
            } label: {
                Label("导出数据", systemImage: "square.and.arrow.up")
            }
        }
    }
    
    private var aboutSection: some View {
        Section("关于") {
            // 应用版本
            HStack {
                Label("版本", systemImage: "info.circle")
                
                Spacer()
                
                Text("1.0.0")
                    .foregroundStyle(.secondary)
            }
            
            // 关于应用
            Button {
                showingAboutView = true
            } label: {
                Label("关于 EchoTales", systemImage: "sparkles")
            }
            
            // 隐私政策
            Button {
                showingPrivacyView = true
            } label: {
                Label("隐私政策", systemImage: "hand.raised")
            }
            
            // 开源许可
            NavigationLink {
                LicensesView()
            } label: {
                Label("开源许可", systemImage: "doc.text")
            }
        }
    }
    
    private var supportSection: some View {
        Section("帮助和支持") {
            // 使用帮助
            Button {
                showingHelpView = true
            } label: {
                Label("使用帮助", systemImage: "questionmark.circle")
            }
            
            // 反馈建议
            Button {
                // 打开邮件应用或反馈表单
            } label: {
                Label("反馈建议", systemImage: "envelope")
            }
            
            // 评价应用
            Button {
                // 跳转到App Store评价
            } label: {
                Label("评价应用", systemImage: "star")
            }
        }
    }
}

/// 关于视图
struct AboutView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 24) {
                    // 应用图标和名称
                    VStack(spacing: 16) {
                        Image(systemName: "sparkles.rectangle.stack")
                            .font(.system(size: 80))
                            .foregroundStyle(.blue.gradient)
                        
                        VStack(spacing: 8) {
                            Text("EchoTales")
                                .font(.largeTitle)
                                .fontWeight(.bold)
                            
                            Text("用你的声音讲述精彩故事")
                                .font(.headline)
                                .foregroundStyle(.secondary)
                                .multilineTextAlignment(.center)
                        }
                    }
                    
                    // 应用介绍
                    VStack(alignment: .leading, spacing: 16) {
                        Text("关于应用")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        Text("""
                        EchoTales 是一款创新的儿童故事应用，结合了先进的AI技术和语音克隆技术，让您能够用自己的声音为孩子讲述个性化的童话故事。
                        
                        主要功能：
                        • 语音克隆：录制您的声音，创建个性化语音模型
                        • AI故事生成：基于主题自动生成精彩童话故事
                        • 多语言支持：支持中文、英文等多种语言
                        • 高质量语音合成：将故事转换为您的声音
                        """)
                            .font(.body)
                            .lineSpacing(4)
                    }
                    
                    // 技术支持
                    VStack(alignment: .leading, spacing: 16) {
                        Text("技术支持")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text("语音技术：")
                                    .fontWeight(.medium)
                                Text("MiniMax API")
                                    .foregroundStyle(.secondary)
                            }
                            
                            HStack {
                                Text("AI生成：")
                                    .fontWeight(.medium)
                                Text("DeepSeek API")
                                    .foregroundStyle(.secondary)
                            }
                            
                            HStack {
                                Text("开发框架：")
                                    .fontWeight(.medium)
                                Text("SwiftUI + iOS 17+")
                                    .foregroundStyle(.secondary)
                            }
                        }
                        .font(.body)
                    }
                    
                    // 版权信息
                    VStack(spacing: 8) {
                        Text("© 2025 EchoTales")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                        
                        Text("版本 1.0.0")
                            .font(.caption)
                            .foregroundStyle(.secondary)
                    }
                }
                .padding()
            }
            .navigationTitle("关于")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
}

/// 隐私政策视图
struct PrivacyView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text("""
                    隐私政策
                    
                    最后更新：2025年7月6日
                    
                    1. 信息收集
                    我们收集您提供的以下信息：
                    • 录音数据：用于创建语音克隆模型
                    • 故事内容：您生成和保存的故事
                    • 使用数据：应用使用统计信息
                    
                    2. 信息使用
                    我们使用收集的信息来：
                    • 提供语音克隆和故事生成服务
                    • 改进应用功能和用户体验
                    • 提供技术支持
                    
                    3. 信息保护
                    我们采取适当的安全措施保护您的个人信息：
                    • 数据加密传输和存储
                    • 限制访问权限
                    • 定期安全审查
                    
                    4. 信息共享
                    我们不会向第三方出售、交易或转让您的个人信息，除非：
                    • 获得您的明确同意
                    • 法律要求
                    • 保护我们的权利和安全
                    
                    5. 数据保留
                    我们仅在必要期间保留您的个人信息：
                    • 录音数据：直到您删除语音克隆
                    • 故事数据：直到您删除故事
                    • 使用数据：最多保留2年
                    
                    6. 您的权利
                    您有权：
                    • 访问您的个人信息
                    • 更正不准确的信息
                    • 删除您的个人信息
                    • 限制信息处理
                    
                    7. 联系我们
                    如有隐私相关问题，请联系：
                    邮箱：<EMAIL>
                    """)
                        .font(.body)
                        .lineSpacing(4)
                }
                .padding()
            }
            .navigationTitle("隐私政策")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
}

/// 帮助视图
struct HelpView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationStack {
            List {
                Section("快速开始") {
                    HelpItemView(
                        title: "如何录制声音？",
                        content: "点击首页的\"录制声音\"按钮，按住录音按钮开始录制。建议录制30秒以上的清晰语音。"
                    )

                    HelpItemView(
                        title: "如何生成故事？",
                        content: "点击\"生成故事\"按钮，输入故事主题，选择语言，然后点击生成即可。"
                    )
                    
                    HelpItemView(
                        title: "如何播放故事？",
                        content: "在故事列表中选择故事，点击播放按钮即可听到用您的声音讲述的故事。"
                    )
                }
                
                Section("常见问题") {
                    HelpItemView(
                        title: "语音克隆需要多长时间？",
                        content: "通常需要1-3分钟，具体时间取决于录音长度和网络状况。"
                    )
                    
                    HelpItemView(
                        title: "支持哪些语言？",
                        content: "目前支持中文（简体）、中文（繁体）和英文。"
                    )
                    
                    HelpItemView(
                        title: "录音质量要求？",
                        content: "建议在安静环境下录制，声音清晰，时长30秒以上效果最佳。"
                    )
                }
                
                Section("故障排除") {
                    HelpItemView(
                        title: "无法录音？",
                        content: "请检查麦克风权限是否已开启，在设置-隐私与安全-麦克风中允许EchoTales访问。"
                    )
                    
                    HelpItemView(
                        title: "故事生成失败？",
                        content: "请检查网络连接，确保主题内容适合儿童，避免使用敏感词汇。"
                    )
                    
                    HelpItemView(
                        title: "语音合成失败？",
                        content: "请确保语音克隆已完成，检查网络连接，或尝试重新生成。"
                    )
                }
            }
            .navigationTitle("使用帮助")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
}

/// 帮助项目视图
struct HelpItemView: View {
    let title: String
    let content: String
    @State private var isExpanded = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Button {
                withAnimation(.easeInOut(duration: 0.2)) {
                    isExpanded.toggle()
                }
            } label: {
                HStack {
                    Text(title)
                        .font(.headline)
                        .fontWeight(.medium)
                        .foregroundStyle(.primary)
                    
                    Spacer()
                    
                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
            }
            .buttonStyle(.plain)
            
            if isExpanded {
                Text(content)
                    .font(.body)
                    .foregroundStyle(.secondary)
                    .lineSpacing(4)
                    .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .padding(.vertical, 4)
    }
}

/// 存储使用情况视图
struct StorageUsageView: View {
    var body: some View {
        List {
            Section("存储详情") {
                HStack {
                    Label("录音文件", systemImage: "waveform")
                    Spacer()
                    Text("12.5 MB")
                        .foregroundStyle(.secondary)
                }
                
                HStack {
                    Label("故事文件", systemImage: "book")
                    Spacer()
                    Text("2.1 MB")
                        .foregroundStyle(.secondary)
                }
                
                HStack {
                    Label("缓存文件", systemImage: "folder")
                    Spacer()
                    Text("5.3 MB")
                        .foregroundStyle(.secondary)
                }
            }
            
            Section("总计") {
                HStack {
                    Text("总使用空间")
                        .fontWeight(.medium)
                    Spacer()
                    Text("19.9 MB")
                        .fontWeight(.medium)
                }
            }
        }
        .navigationTitle("存储使用情况")
        .navigationBarTitleDisplayMode(.inline)
    }
}

/// 开源许可视图
struct LicensesView: View {
    var body: some View {
        List {
            Section("第三方库") {
                VStack(alignment: .leading, spacing: 8) {
                    Text("SwiftUI")
                        .font(.headline)
                    Text("Apple Inc. - iOS SDK")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("Foundation")
                        .font(.headline)
                    Text("Apple Inc. - iOS SDK")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("AVFoundation")
                        .font(.headline)
                    Text("Apple Inc. - iOS SDK")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
            }
            
            Section("API服务") {
                VStack(alignment: .leading, spacing: 8) {
                    Text("MiniMax API")
                        .font(.headline)
                    Text("语音克隆和合成服务")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("DeepSeek API")
                        .font(.headline)
                    Text("AI故事生成服务")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
            }
        }
        .navigationTitle("开源许可")
        .navigationBarTitleDisplayMode(.inline)
    }
}

#Preview {
    SettingsView()
        .environment(AppViewModel())
}
