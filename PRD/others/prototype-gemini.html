<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 童话故事 App 原型</title>
    <style>
        /* --- 全局样式与字体 --- */
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');

        :root {
            --system-blue: #007AFF;
            --system-gray-light: #f2f2f7;
            --system-gray-bg: #f9f9f9;
            --system-gray-border: #e0e0e0;
            --system-text-primary: #1c1c1e;
            --system-text-secondary: #8e8e93;
            --safe-area-padding: 20px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Noto Sans SC', 'Helvetica Neue', sans-serif;
            background-color: var(--system-gray-light);
            margin: 0;
            padding: 40px 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }
        
        /* --- 页面排布与结构规范 --- */
        .pages-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
            gap: 40px 20px;
            width: 100%;
            max-width: 1280px;
            justify-content: center;
        }

        .page-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
        }

        .page {
            width: 375px;
            height: 720px; /* 模拟 iPhone 13 mini 左右的高度感 */
            background-color: #fff;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            border-radius: 48px;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .title {
            font-size: 14px;
            font-weight: 500;
            color: var(--system-text-secondary);
        }

        /* --- iPhone 模拟区规范 --- */
        .iphone-frame {
            width: 100%;
            height: 100%;
            background-color: var(--system-gray-bg);
            border: 10px solid #111;
            border-radius: 48px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* --- 通用组件库规范 --- */
        .navbar {
            height: 48px;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 var(--safe-area-padding);
            box-sizing: border-box;
        }
        .navbar .nav-title {
            font-size: 18px;
            font-weight: 700;
            color: var(--system-text-primary);
        }

        .content {
            flex-grow: 1;
            padding: 0 var(--safe-area-padding);
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        /* 隐藏滚动条 */
        .content::-webkit-scrollbar {
            display: none;
        }

        .input, textarea.input {
            width: 100%;
            padding: 16px;
            font-size: 16px;
            background-color: #f0f0f5;
            border: 1px solid #e0e0e5;
            border-radius: 14px;
            box-sizing: border-box;
            color: var(--system-text-primary);
            font-family: inherit;
        }
        textarea.input {
            min-height: 120px;
            resize: vertical;
        }
        .input::placeholder {
            color: var(--system-text-secondary);
        }

        .button {
            width: 100%;
            padding: 15px;
            font-size: 17px;
            font-weight: 600;
            background-color: var(--system-blue);
            color: white;
            border: none;
            border-radius: 28px;
            cursor: pointer;
            text-align: center;
            transition: background-color 0.2s;
            box-sizing: border-box;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .button.secondary {
             background-color: var(--system-gray-light);
             color: var(--system-blue);
             border: 1px solid var(--system-gray-border);
        }

        .tabbar {
            height: 60px;
            flex-shrink: 0;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 10px; /* iPhone home indicator safe area */
            border-top: 1px solid var(--system-gray-border);
            background-color: rgba(249, 249, 249, 0.95); /* Frosted glass effect */
            backdrop-filter: blur(10px);
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            font-size: 10px;
            color: var(--system-text-secondary);
            cursor: pointer;
        }
        .tab-item.active {
            color: var(--system-blue);
        }
        .tab-item svg {
            width: 24px;
            height: 24px;
        }

        /* --- 特定组件样式 --- */
        .welcome-content {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            height: 100%;
            gap: 16px;
        }
        .welcome-content h1 {
            font-size: 32px;
            font-weight: 700;
            color: var(--system-text-primary);
            margin: 0;
        }
        .welcome-content p {
            font-size: 16px;
            color: var(--system-text-secondary);
            max-width: 80%;
            line-height: 1.5;
            margin: 0 0 30px 0;
        }

        .card {
            background-color: #fff;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.06);
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .story-card {
            flex-direction: row;
            align-items: center;
            gap: 16px;
        }
        .story-card .icon {
            width: 48px;
            height: 48px;
            background-color: var(--system-gray-light);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .story-card .icon svg {
            width: 28px;
            height: 28px;
            color: var(--system-blue);
        }
        .story-card .text-content {
            flex-grow: 1;
        }
        .story-card .title {
            font-size: 17px;
            font-weight: 600;
            color: var(--system-text-primary);
            margin: 0;
        }
        .story-card .subtitle {
            font-size: 14px;
            color: var(--system-text-secondary);
            margin: 0;
        }
        .story-card .play-button {
            width: 36px;
            height: 36px;
            background-color: var(--system-blue);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .fab { /* Floating Action Button */
            position: absolute;
            bottom: 80px; /* Above tab bar */
            right: var(--safe-area-padding);
            width: 56px;
            height: 56px;
            background-color: var(--system-blue);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.4);
            color: white;
            cursor: pointer;
        }
        .fab svg {
            width: 28px;
            height: 28px;
        }
        
        .record-section {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            gap: 24px;
        }
        .mic-button {
            width: 120px;
            height: 120px;
            background-color: var(--system-blue);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            cursor: pointer;
            box-shadow: 0 0 0 16px rgba(0, 122, 255, 0.2);
        }
        .mic-button.recording {
             background-color: #ff3b30; /* System Red */
             box-shadow: 0 0 0 16px rgba(255, 59, 48, 0.2);
        }
        .record-section .instructions {
            font-size: 14px;
            color: var(--system-text-secondary);
            line-height: 1.6;
        }
        
        .settings-group {
            background-color: #fff;
            border-radius: 12px;
            overflow: hidden;
        }
        .settings-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 14px var(--safe-area-padding);
            font-size: 16px;
            color: var(--system-text-primary);
            cursor: pointer;
        }
        .settings-item:not(:last-child) {
            border-bottom: 1px solid var(--system-gray-light);
        }
        .settings-item .value {
            color: var(--system-text-secondary);
        }
        .settings-item .chevron {
            color: #c7c7cc;
        }

        .player-content {
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        .player-story-text {
            flex-grow: 1;
            font-size: 18px;
            line-height: 1.8;
            color: var(--system-text-primary);
            overflow-y: auto;
            padding-bottom: 20px;
        }
        .player-controls {
            flex-shrink: 0;
            padding: 20px 0;
        }
        .progress-bar {
            width: 100%;
            height: 4px;
            background-color: var(--system-gray-border);
            border-radius: 2px;
            margin-bottom: 8px;
            position: relative;
        }
        .progress {
            width: 40%;
            height: 100%;
            background-color: var(--system-blue);
            border-radius: 2px;
        }
        .time-labels {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: var(--system-text-secondary);
            margin-bottom: 16px;
        }
        .control-buttons {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 40px;
            color: var(--system-text-primary);
        }
        .play-pause-main {
            width: 64px;
            height: 64px;
            background-color: var(--system-blue);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .loader-container {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 20px;
        }
        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid var(--system-gray-light);
            border-top: 5px solid var(--system-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .loader-container p {
            color: var(--system-text-secondary);
            font-size: 16px;
        }

    </style>
</head>
<body>

    <div class="pages-container">

        <!-- PAGE 1: 欢迎页 -->
        <div class="page-wrapper">
            <div class="page">
                <div class="iphone-frame">
                    <div class="content welcome-content">
                        <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="color: var(--system-blue);">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" fill="currentColor"/>
                            <path d="M12.5 17.5c-2.76 0-5-2.24-5-5s2.24-5 5-5c1.38 0 2.63.56 3.54 1.46L17.5 10l-1.41-1.41-1.46 1.46C13.88 9.3 12.7 8.5 11.5 8.5c-1.93 0-3.5 1.57-3.5 3.5s1.57 3.5 3.5 3.5c1.93 0 3.5-1.57 3.5-3.5h-1.5c0 1.1-.9 2-2 2s-2-.9-2-2 .9-2 2-2v-1.5c-2.76 0-5 2.24-5 5s2.24 5 5 5c2.76 0 5-2.24 5-5h-1.5c0 2.76-2.24 5-5 5z" fill="currentColor" fill-opacity="0.3"/>
                            <path d="M11 11.5c.83 0 1.5-.67 1.5-1.5S11.83 8.5 11 8.5s-1.5.67-1.5 1.5.67 1.5 1.5 1.5z" fill="currentColor"/>
                        </svg>
                        <h1>童话之声</h1>
                        <p>录制您的声音，让 AI 为孩子讲述独一无二的睡前故事。</p>
                        <div style="width: 80%; position: absolute; bottom: 60px;">
                            <button class="button">开始使用</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="title">1. 欢迎页</div>
        </div>
        
        <!-- PAGE 2: 主页 - 故事列表 -->
        <div class="page-wrapper">
            <div class="page">
                <div class="iphone-frame">
                    <nav class="navbar">
                        <h1 class="nav-title">童话小屋</h1>
                    </nav>
                    <main class="content">
                        <div class="story-card">
                            <div class="icon">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M19 2H5c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM8 18H6v-2h2v2zm0-4H6v-2h2v2zm0-4H6V8h2v2zm10 6h-8v-2h8v2zm0-4h-8v-2h8v2zm0-4h-8V8h8v2z"></path></svg>
                            </div>
                            <div class="text-content">
                                <h3 class="title">勇敢的小狐狸</h3>
                                <p class="subtitle">由 爸爸 的声音讲述</p>
                            </div>
                            <div class="play-button">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="20" height="20"><path d="M8 5v14l11-7z"></path></svg>
                            </div>
                        </div>
                        <div class="story-card">
                            <div class="icon">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M19 2H5c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM8 18H6v-2h2v2zm0-4H6v-2h2v2zm0-4H6V8h2v2zm10 6h-8v-2h8v2zm0-4h-8v-2h8v2zm0-4h-8V8h8v2z"></path></svg>
                            </div>
                            <div class="text-content">
                                <h3 class="title">月亮上的兔子</h3>
                                <p class="subtitle">由 妈妈 的声音讲述</p>
                            </div>
                            <div class="play-button">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="20" height="20"><path d="M8 5v14l11-7z"></path></svg>
                            </div>
                        </div>
                    </main>
                    <div class="fab">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"></path></svg>
                    </div>
                    <nav class="tabbar">
                        <div class="tab-item active">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"></path></svg>
                            <span>主页</span>
                        </div>
                        <div class="tab-item">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line></svg>
                            <span>我的声音</span>
                        </div>
                        <div class="tab-item">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                            <span>设置</span>
                        </div>
                    </nav>
                </div>
            </div>
            <div class="title">2. 主页 (故事列表)</div>
        </div>

        <!-- PAGE 3: 创建故事 -->
        <div class="page-wrapper">
            <div class="page">
                <div class="iphone-frame">
                    <nav class="navbar">
                        <h1 class="nav-title">创建故事</h1>
                    </nav>
                    <main class="content" style="padding-top: 20px;">
                        <p style="color: var(--system-text-secondary); font-size: 15px; margin: 0;">告诉我们你想听一个什么样的故事？AI 将为你编写一个独特的童话。</p>
                        <textarea class="input" placeholder="例如：一个关于勇敢小刺猬和胆小大灰狼成为朋友的故事..."></textarea>
                        <div style="flex-grow: 1;"></div>
                        <button class="button">生成童话故事</button>
                    </main>
                </div>
            </div>
            <div class="title">3. 创建故事</div>
        </div>

        <!-- PAGE 4: 声音克隆/录制 -->
        <div class="page-wrapper">
            <div class="page">
                <div class="iphone-frame">
                    <nav class="navbar">
                        <h1 class="nav-title">克隆我的声音</h1>
                    </nav>
                    <main class="content">
                        <div class="record-section">
                            <div class="mic-button">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="48" height="48"><path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.49 6-3.31 6-6.72h-1.7z"></path></svg>
                            </div>
                            <p class="instructions">点击按钮开始录制。<br>请用清晰、自然的语调朗读屏幕上出现的文字，大约需要3分钟。</p>
                        </div>
                        <input class="input" placeholder="为这个声音命名，如“爸爸的声音”">
                        <button class="button">开始录制</button>
                    </main>
                </div>
            </div>
            <div class="title">4. 声音克隆</div>
        </div>

        <!-- PAGE 5: 我的声音管理 -->
        <div class="page-wrapper">
            <div class="page">
                <div class="iphone-frame">
                    <nav class="navbar">
                        <h1 class="nav-title">我的声音</h1>
                    </nav>
                    <main class="content" style="padding-top: 20px;">
                        <p style="color: var(--system-text-secondary); font-size: 15px; margin: 0;">管理已克隆的声音，或录制新的声音。</p>
                        <div class="card">
                            <h3 class="title" style="margin: 0;">爸爸的声音</h3>
                            <p class="subtitle" style="margin: 0;">克隆于 2023年10月27日</p>
                        </div>
                        <div class="card">
                            <h3 class="title" style="margin: 0;">妈妈的声音</h3>
                            <p class="subtitle" style="margin: 0;">克隆于 2023年10月26日</p>
                        </div>
                        <div style="flex-grow: 1;"></div>
                        <button class="button secondary">录制新声音</button>
                    </main>
                </div>
            </div>
            <div class="title">5. 我的声音</div>
        </div>

        <!-- PAGE 6: 故事播放页 -->
        <div class="page-wrapper">
            <div class="page">
                <div class="iphone-frame">
                    <nav class="navbar">
                        <h1 class="nav-title">勇敢的小狐狸</h1>
                    </nav>
                    <main class="content player-content">
                        <div class="player-story-text">
                            <p>在一片茂密的森林深处，住着一只名叫奇奇的小狐狸。他不像别的狐狸那样狡猾，反而充满了好奇心和勇气。大家都说，森林里的黑熊是最可怕的，但奇奇却不这么认为。</p>
                            <p>有一天，他听说黑熊先生生病了，需要一种长在悬崖上的草药。没有动物敢去，奇奇却背上了他的小背包，决定去帮助黑熊先生...</p>
                        </div>
                        <div class="player-controls">
                            <div class="progress-bar">
                                <div class="progress"></div>
                            </div>
                            <div class="time-labels">
                                <span>1:23</span>
                                <span>-2:45</span>
                            </div>
                            <div class="control-buttons">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="32" height="32"><path d="M6 6h2v12H6zm3.5 6l8.5 6V6z"></path></svg>
                                <div class="play-pause-main">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="32" height="32"><path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"></path></svg>
                                </div>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="32" height="32"><path d="M16 6h2v12h-2zm-3.5 6l-8.5 6V6z"></path></svg>
                            </div>
                        </div>
                    </main>
                </div>
            </div>
            <div class="title">6. 故事播放</div>
        </div>

        <!-- PAGE 7: 设置页 -->
        <div class="page-wrapper">
            <div class="page">
                <div class="iphone-frame">
                    <nav class="navbar">
                        <h1 class="nav-title">设置</h1>
                    </nav>
                    <main class="content" style="padding-top: 20px; gap: 30px;">
                        <div class="settings-group">
                            <div class="settings-item">
                                <span>我的声音</span>
                                <svg class="chevron" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="20" height="20"><path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"></path></svg>
                            </div>
                            <div class="settings-item">
                                <span>语言选择</span>
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <span class="value">中文 (普通话)</span>
                                    <svg class="chevron" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="20" height="20"><path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"></path></svg>
                                </div>
                            </div>
                        </div>
                        <div class="settings-group">
                            <div class="settings-item">
                                <span>关于我们</span>
                                <svg class="chevron" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="20" height="20"><path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"></path></svg>
                            </div>
                             <div class="settings-item">
                                <span>帮助与反馈</span>
                                <svg class="chevron" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="20" height="20"><path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"></path></svg>
                            </div>
                        </div>
                         <div class="settings-group">
                            <div class="settings-item" style="color: #ff3b30; justify-content: center;">
                                <span>退出登录</span>
                            </div>
                        </div>
                    </main>
                </div>
            </div>
            <div class="title">7. 设置</div>
        </div>

        <!-- PAGE 8: 语言选择 -->
        <div class="page-wrapper">
            <div class="page">
                <div class="iphone-frame">
                    <nav class="navbar">
                        <h1 class="nav-title">语言选择</h1>
                    </nav>
                    <main class="content" style="padding-top: 20px;">
                        <div class="settings-group">
                            <div class="settings-item">
                                <span>中文 (普通话)</span>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="24" height="24" style="color: var(--system-blue);">
                                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"></path>
                                </svg>
                            </div>
                             <div class="settings-item">
                                <span>中文 (粤语)</span>
                            </div>
                             <div class="settings-item">
                                <span>English</span>
                            </div>
                        </div>
                        <div style="flex-grow: 1;"></div>
                        <button class="button">保存设置</button>
                    </main>
                </div>
            </div>
            <div class="title">8. 语言选择</div>
        </div>

        <!-- PAGE 9: 加载/处理中 -->
        <div class="page-wrapper">
            <div class="page">
                <div class="iphone-frame">
                    <nav class="navbar">
                        <h1 class="nav-title">正在处理</h1>
                    </nav>
                    <main class="content">
                       <div class="loader-container">
                           <div class="spinner"></div>
                           <p>正在为您生成故事...<br>这可能需要一点时间，请稍候。</p>
                       </div>
                    </main>
                </div>
            </div>
            <div class="title">9. 处理中状态</div>
        </div>

    </div>

</body>
</html>