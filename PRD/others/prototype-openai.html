<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>声音童话App原型</title>
  <style>
    body {
      background: #f2f2f7;
      font-family: -apple-system, BlinkMacSystemFont, sans-serif;
      margin: 0;
      padding: 32px;
      display: flex;
      flex-wrap: wrap;
      gap: 32px;
      justify-content: center;
    }

    .page {
      width: 360px;
      height: 640px;
      border-radius: 32px;
      background: white;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .iphone-frame {
      flex: 1;
      padding: 16px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
    }

    .navbar {
      height: 48px;
      font-weight: bold;
      font-size: 20px;
      text-align: center;
      line-height: 48px;
      margin-bottom: 12px;
    }

    .content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 12px;
      overflow-y: auto;
    }

    .input {
      border: none;
      background: #f2f2f7;
      padding: 12px 16px;
      border-radius: 14px;
      font-size: 16px;
    }

    .button {
      background-color: #007aff;
      color: white;
      border: none;
      padding: 14px;
      font-size: 16px;
      border-radius: 28px;
      width: 100%;
      cursor: pointer;
    }

    .tabbar {
      height: 48px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      border-top: 1px solid #ddd;
      font-size: 12px;
    }

    .tabbar div {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #007aff;
    }

    .title {
      text-align: center;
      padding: 8px;
      font-size: 14px;
      color: #999;
    }

    .card {
      background: #f9f9f9;
      border-radius: 16px;
      padding: 16px;
      font-size: 14px;
    }
  </style>
</head>
<body>

  <!-- 页面 1：欢迎页 -->
  <div class="page">
    <div class="iphone-frame">
      <div class="navbar">声音童话</div>
      <div class="content">
        <p class="card">欢迎使用声音童话 App，记录你的声音，为孩子讲故事。</p>
        <button class="button">立即开始</button>
      </div>
    </div>
    <div class="title">欢迎页</div>
  </div>

  <!-- 页面 2：录制声音 -->
  <div class="page">
    <div class="iphone-frame">
      <div class="navbar">录制声音</div>
      <div class="content">
        <p>请选择要录制的声音身份：</p>
        <button class="button">我是爸爸</button>
        <button class="button">我是妈妈</button>
        <p class="card">请录制一段 30 秒以上的清晰语音。</p>
        <button class="button">开始录音</button>
      </div>
    </div>
    <div class="title">录制声音</div>
  </div>

  <!-- 页面 3：声音克隆中 -->
  <div class="page">
    <div class="iphone-frame">
      <div class="navbar">声音克隆</div>
      <div class="content">
        <p class="card">正在上传录音并进行声音克隆处理，请稍候…</p>
        <button class="button">取消</button>
      </div>
    </div>
    <div class="title">声音克隆中</div>
  </div>

  <!-- 页面 4：声音克隆成功 -->
  <div class="page">
    <div class="iphone-frame">
      <div class="navbar">克隆完成</div>
      <div class="content">
        <p class="card">声音克隆成功！现在可以用这个声音为孩子讲故事啦～</p>
        <button class="button">去写故事</button>
      </div>
    </div>
    <div class="title">克隆成功</div>
  </div>

  <!-- 页面 5：输入故事提示 -->
  <div class="page">
    <div class="iphone-frame">
      <div class="navbar">故事创作</div>
      <div class="content">
        <input class="input" placeholder="请输入你想讲的故事主题，如森林探险…" />
        <button class="button">生成故事</button>
      </div>
    </div>
    <div class="title">故事创作</div>
  </div>

  <!-- 页面 6：生成故事中 -->
  <div class="page">
    <div class="iphone-frame">
      <div class="navbar">生成中</div>
      <div class="content">
        <p class="card">正在生成童话故事，请稍候...</p>
      </div>
    </div>
    <div class="title">生成中</div>
  </div>

  <!-- 页面 7：故事预览 -->
  <div class="page">
    <div class="iphone-frame">
      <div class="navbar">故事预览</div>
      <div class="content">
        <div class="card">
          很久很久以前，有一个小熊在森林里迷路了……
        </div>
        <button class="button">用爸爸/妈妈的声音朗读</button>
      </div>
    </div>
    <div class="title">故事预览</div>
  </div>

  <!-- 页面 8：播放语音 -->
  <div class="page">
    <div class="iphone-frame">
      <div class="navbar">正在讲故事</div>
      <div class="content">
        <p class="card">正在播放故事语音…</p>
        <button class="button">暂停</button>
        <button class="button">重播</button>
      </div>
    </div>
    <div class="title">播放语音</div>
  </div>

  <!-- 页面 9：多语言支持 -->
  <div class="page">
    <div class="iphone-frame">
      <div class="navbar">语言设置</div>
      <div class="content">
        <p class="card">请选择讲故事的语言：</p>
        <button class="button">中文普通话</button>
        <button class="button">粤语</button>
        <button class="button">英语</button>
      </div>
    </div>
    <div class="title">语言设置</div>
  </div>



    <!-- 页面 10：设置页 -->
  <div class="page">
    <div class="iphone-frame">
      <div class="navbar">设置</div>
      <div class="content">
        <p class="card">账户信息</p>
        <div class="input">昵称：宝爸</div>
        <div class="input">已克隆声音：爸爸的声音</div>

        <p class="card">偏好设置</p>
        <button class="button">语言偏好设置</button>
        <button class="button">切换身份（爸爸/妈妈）</button>

        <p class="card">关于</p>
        <button class="button">应用版本 v1.0</button>
        <button class="button">联系我们</button>
      </div>
    </div>
    <div class="title">设置页</div>
  </div>

  <!-- 页面 11：音频管理页 -->
  <div class="page">
    <div class="iphone-frame">
      <div class="navbar">音频管理</div>
      <div class="content">
        <p class="card">历史故事语音</p>
        <div class="card">
          <p>🌲 森林探险故事</p>
          <button class="button">播放</button>
          <button class="button">删除</button>
        </div>

        <div class="card">
          <p>🦖 小恐龙找妈妈</p>
          <button class="button">播放</button>
          <button class="button">删除</button>
        </div>

        <button class="button">上传自定义音频</button>
      </div>
    </div>
    <div class="title">音频管理页</div>
  </div>


</body>
</html>
