# Xcode
#
# gitignore contributors: remember to update Global/Xcode.gitignore, Objective-C.gitignore & Swift.gitignore

## User settings
xcuserdata/

## compatibility with Xcode 8 and earlier (ignoring not required starting Xcode 9)
*.xcscmblueprint
*.xccheckout

## compatibility with Xcode 3 and earlier (ignoring not required starting Xcode 4)
build/
DerivedData/
*.moved-aside
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3

## Obj-C/Swift specific
*.hmap

## App packaging
*.ipa
*.dSYM.zip
*.dSYM

## Playgrounds
timeline.xctimeline
playground.xcworkspace

# Swift Package Manager
#
# Add this line if you want to avoid checking in source code from Swift Package Manager dependencies.
# Packages/
# Package.pins
# Package.resolved
# *.xcodeproj
#
# Xcode automatically generates this directory with a .xcworkspacedata file and xcuserdata
# hence it is not needed unless you have added a package configuration file to your project
# .swiftpm

.build/

# CocoaPods
#
# We recommend against adding the Pods directory to your .gitignore. However
# you should judge for yourself, the pros and cons are mentioned at:
# https://guides.cocoapods.org/using/using-cocoapods.html#should-i-check-the-pods-directory-into-source-control
#
# Pods/
#
# Add this line if you want to avoid checking in source code from the Xcode workspace
# *.xcworkspace

# Carthage
#
# Add this line if you want to avoid checking in source code from Carthage dependencies.
# Carthage/Checkouts

Carthage/Build/

# Accio dependency management
Dependencies/
.accio/

# fastlane
#
# It is recommended to not store the screenshots in the git repo.
# Instead, use fastlane to re-generate the screenshots whenever they are needed.
# For more information about the recommended setup visit:
# https://docs.fastlane.tools/best-practices/source-control/

fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots/**/*.png
fastlane/test_output

# Code Injection
#
# After new code Injection tools there's a generated folder /iOSInjectionProject
# https://github.com/johnno1962/injectionforxcode

iOSInjectionProject/

# macOS
.DS_Store

# API Keys (security)
APIKeys.plist
Config.plist
secrets.json

# Temporary files
*.tmp
*.temp
*~

# Log files
*.log

# IDE files
.vscode/
.idea/

# Backup files
*.backup
*.bak

# Audio recordings (user generated content)
Recordings/
*.wav
*.m4a
*.mp3
*.aac

# Generated content
GeneratedStories/
VoiceClones/

# Test coverage
*.gcov
*.gcda
*.gcno

# Instruments
*.trace

# Simulator logs
simulator_logs/

# Provisioning profiles
*.mobileprovision
*.provisionprofile

# Certificate files
*.p12
*.cer
*.certSigningRequest

# App Store Connect API Key
AuthKey_*.p8

# Firebase
GoogleService-Info.plist
google-services.json

# Crashlytics
crashlytics-build.properties
fabric.properties

# R.swift
*.generated.swift

# SwiftGen
swiftgen.yml

# Sourcery
.sourcery_cache/

# Mint
.mint/

# Ruby
Gemfile.lock
.bundle/

# Python
__pycache__/
*.py[cod]
*$py.class

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Local configuration
local.properties
