//
//  EchoTalesTests.swift
//  EchoTalesTests
//
//  Created by tim on 2025/7/6.
//

import Testing
import Foundation
@testable import EchoTales

struct EchoTalesTests {

    // MARK: - Basic Tests

    @Test func testAppViewModelInitialization() async throws {
        await MainActor.run {
            let appViewModel = AppViewModel()
            #expect(appViewModel.stories.count == 0)
            #expect(appViewModel.voiceClones.count == 0)
            #expect(appViewModel.recordings.count == 0)
        }
    }

    @Test func testAPIKeyStorage() async throws {
        await MainActor.run {
            let appViewModel = AppViewModel()

            // Test API key storage
            appViewModel.minimaxApiKey = "test_api_key_123"
            #expect(appViewModel.minimaxApiKey == "test_api_key_123")

            appViewModel.deepseekApiKey = "sk-test123"
            #expect(appViewModel.deepseekApiKey == "sk-test123")
        }
    }

    @Test func testLanguageModel() async throws {
        let supportedLanguages = Language.supportedLanguages
        #expect(supportedLanguages.count > 0)

        let chineseLanguage = supportedLanguages.first { $0.code == "zh-CN" }
        #expect(chineseLanguage != nil)
        #expect(chineseLanguage?.displayName == "中文（普通话）")

        let englishLanguage = supportedLanguages.first { $0.code == "en-US" }
        #expect(englishLanguage != nil)
        #expect(englishLanguage?.displayName == "English")
    }

    @Test func testStoryModel() async throws {
        let language = Language.supportedLanguages.first!
        let story = Story(
            title: "Test Story",
            theme: "测试主题",
            language: language
        )

        #expect(story.title == "Test Story")
        #expect(story.theme == "测试主题")
        #expect(story.language == language)
    }
}
