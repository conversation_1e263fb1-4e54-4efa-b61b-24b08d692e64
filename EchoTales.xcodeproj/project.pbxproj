// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		222762A22E1A20B00037181C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2227628C2E1A20AF0037181C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 222762932E1A20AF0037181C;
			remoteInfo = EchoTales;
		};
		222762AC2E1A20B00037181C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2227628C2E1A20AF0037181C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 222762932E1A20AF0037181C;
			remoteInfo = EchoTales;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		222762942E1A20AF0037181C /* EchoTales.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = EchoTales.app; sourceTree = BUILT_PRODUCTS_DIR; };
		222762A12E1A20B00037181C /* EchoTalesTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = EchoTalesTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		222762AB2E1A20B00037181C /* EchoTalesUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = EchoTalesUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		222762962E1A20AF0037181C /* EchoTales */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = EchoTales;
			sourceTree = "<group>";
		};
		222762A42E1A20B00037181C /* EchoTalesTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = EchoTalesTests;
			sourceTree = "<group>";
		};
		222762AE2E1A20B00037181C /* EchoTalesUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = EchoTalesUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		222762912E1A20AF0037181C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2227629E2E1A20B00037181C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		222762A82E1A20B00037181C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2227628B2E1A20AF0037181C = {
			isa = PBXGroup;
			children = (
				222762962E1A20AF0037181C /* EchoTales */,
				222762A42E1A20B00037181C /* EchoTalesTests */,
				222762AE2E1A20B00037181C /* EchoTalesUITests */,
				222762952E1A20AF0037181C /* Products */,
			);
			sourceTree = "<group>";
		};
		222762952E1A20AF0037181C /* Products */ = {
			isa = PBXGroup;
			children = (
				222762942E1A20AF0037181C /* EchoTales.app */,
				222762A12E1A20B00037181C /* EchoTalesTests.xctest */,
				222762AB2E1A20B00037181C /* EchoTalesUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		222762932E1A20AF0037181C /* EchoTales */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 222762B52E1A20B00037181C /* Build configuration list for PBXNativeTarget "EchoTales" */;
			buildPhases = (
				222762902E1A20AF0037181C /* Sources */,
				222762912E1A20AF0037181C /* Frameworks */,
				222762922E1A20AF0037181C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				222762962E1A20AF0037181C /* EchoTales */,
			);
			name = EchoTales;
			packageProductDependencies = (
			);
			productName = EchoTales;
			productReference = 222762942E1A20AF0037181C /* EchoTales.app */;
			productType = "com.apple.product-type.application";
		};
		222762A02E1A20B00037181C /* EchoTalesTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 222762B82E1A20B00037181C /* Build configuration list for PBXNativeTarget "EchoTalesTests" */;
			buildPhases = (
				2227629D2E1A20B00037181C /* Sources */,
				2227629E2E1A20B00037181C /* Frameworks */,
				2227629F2E1A20B00037181C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				222762A32E1A20B00037181C /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				222762A42E1A20B00037181C /* EchoTalesTests */,
			);
			name = EchoTalesTests;
			packageProductDependencies = (
			);
			productName = EchoTalesTests;
			productReference = 222762A12E1A20B00037181C /* EchoTalesTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		222762AA2E1A20B00037181C /* EchoTalesUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 222762BB2E1A20B00037181C /* Build configuration list for PBXNativeTarget "EchoTalesUITests" */;
			buildPhases = (
				222762A72E1A20B00037181C /* Sources */,
				222762A82E1A20B00037181C /* Frameworks */,
				222762A92E1A20B00037181C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				222762AD2E1A20B00037181C /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				222762AE2E1A20B00037181C /* EchoTalesUITests */,
			);
			name = EchoTalesUITests;
			packageProductDependencies = (
			);
			productName = EchoTalesUITests;
			productReference = 222762AB2E1A20B00037181C /* EchoTalesUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		2227628C2E1A20AF0037181C /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					222762932E1A20AF0037181C = {
						CreatedOnToolsVersion = 16.4;
					};
					222762A02E1A20B00037181C = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 222762932E1A20AF0037181C;
					};
					222762AA2E1A20B00037181C = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 222762932E1A20AF0037181C;
					};
				};
			};
			buildConfigurationList = 2227628F2E1A20AF0037181C /* Build configuration list for PBXProject "EchoTales" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 2227628B2E1A20AF0037181C;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 222762952E1A20AF0037181C /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				222762932E1A20AF0037181C /* EchoTales */,
				222762A02E1A20B00037181C /* EchoTalesTests */,
				222762AA2E1A20B00037181C /* EchoTalesUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		222762922E1A20AF0037181C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2227629F2E1A20B00037181C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		222762A92E1A20B00037181C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		222762902E1A20AF0037181C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2227629D2E1A20B00037181C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		222762A72E1A20B00037181C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		222762A32E1A20B00037181C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 222762932E1A20AF0037181C /* EchoTales */;
			targetProxy = 222762A22E1A20B00037181C /* PBXContainerItemProxy */;
		};
		222762AD2E1A20B00037181C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 222762932E1A20AF0037181C /* EchoTales */;
			targetProxy = 222762AC2E1A20B00037181C /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		222762B32E1A20B00037181C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = X6GBL4M9U9;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		222762B42E1A20B00037181C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = X6GBL4M9U9;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		222762B62E1A20B00037181C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = X6GBL4M9U9;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "EchoTales需要访问麦克风来录制您的声音，用于语音克隆和故事朗读功能。";
				INFOPLIST_KEY_NSNetworkUsageDescription = "EchoTales需要网络连接来使用AI语音克隆和故事生成功能。";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.timchen.app.EchoTales;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		222762B72E1A20B00037181C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = X6GBL4M9U9;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "EchoTales需要访问麦克风来录制您的声音，用于语音克隆和故事朗读功能。";
				INFOPLIST_KEY_NSNetworkUsageDescription = "EchoTales需要网络连接来使用AI语音克隆和故事生成功能。";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.timchen.app.EchoTales;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		222762B92E1A20B00037181C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = X6GBL4M9U9;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.timchen.app.EchoTalesTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/EchoTales.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/EchoTales";
			};
			name = Debug;
		};
		222762BA2E1A20B00037181C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = X6GBL4M9U9;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.timchen.app.EchoTalesTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/EchoTales.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/EchoTales";
			};
			name = Release;
		};
		222762BC2E1A20B00037181C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = X6GBL4M9U9;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.timchen.app.EchoTalesUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = EchoTales;
			};
			name = Debug;
		};
		222762BD2E1A20B00037181C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = X6GBL4M9U9;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.timchen.app.EchoTalesUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = EchoTales;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2227628F2E1A20AF0037181C /* Build configuration list for PBXProject "EchoTales" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				222762B32E1A20B00037181C /* Debug */,
				222762B42E1A20B00037181C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		222762B52E1A20B00037181C /* Build configuration list for PBXNativeTarget "EchoTales" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				222762B62E1A20B00037181C /* Debug */,
				222762B72E1A20B00037181C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		222762B82E1A20B00037181C /* Build configuration list for PBXNativeTarget "EchoTalesTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				222762B92E1A20B00037181C /* Debug */,
				222762BA2E1A20B00037181C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		222762BB2E1A20B00037181C /* Build configuration list for PBXNativeTarget "EchoTalesUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				222762BC2E1A20B00037181C /* Debug */,
				222762BD2E1A20B00037181C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 2227628C2E1A20AF0037181C /* Project object */;
}
