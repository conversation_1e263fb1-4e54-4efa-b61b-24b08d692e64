# EchoTales - AI声音克隆故事应用

EchoTales是一款基于iOS的AI声音克隆和故事生成应用，让用户能够录制自己的声音，创建个性化的声音克隆，并生成由AI朗读的定制故事。

## 功能特性

### 🎙️ 声音录制与克隆
- **15秒录音**：固定15秒录音时长，确保最佳声音克隆效果
- **多语言支持**：支持中文（普通话）、中文（粤语）、英语
- **实时录音监控**：显示录音进度和音量级别
- **声音克隆**：使用MiniMax API进行高质量声音克隆

### 📚 AI故事生成
- **主题定制**：用户输入故事主题，AI生成个性化故事
- **多语言故事**：支持多种语言的故事生成
- **DeepSeek集成**：使用DeepSeek API进行智能故事创作
- **故事管理**：保存、编辑和删除生成的故事

### 🔊 语音合成
- **个性化朗读**：使用克隆的声音朗读生成的故事
- **高质量音频**：MiniMax TTS技术确保自然流畅的语音输出
- **播放控制**：支持播放、暂停、停止等基本控制

### ⚙️ 设置与配置
- **API密钥管理**：安全存储MiniMax和DeepSeek API密钥
- **语言设置**：全局语言偏好设置
- **数据管理**：清除录音、声音克隆和故事数据

## 技术架构

### 开发环境
- **平台**：iOS 18.5+
- **开发语言**：Swift 5
- **UI框架**：SwiftUI
- **架构模式**：MVVM + @Observable

### 核心技术
- **声音处理**：AVFoundation
- **网络请求**：URLSession + async/await
- **数据持久化**：UserDefaults + FileManager
- **状态管理**：@Observable (iOS 17+)

### API集成
- **MiniMax API**：声音克隆和语音合成
- **DeepSeek API**：AI故事生成（可选）

## 项目结构

```
EchoTales/
├── App/
│   ├── EchoTalesApp.swift          # 应用入口
│   └── ContentView.swift           # 主视图
├── Models/
│   ├── Story.swift                 # 故事数据模型
│   ├── VoiceClone.swift           # 声音克隆模型
│   ├── AudioRecording.swift       # 录音模型
│   └── Language.swift             # 语言模型
├── ViewModels/
│   ├── AppViewModel.swift         # 应用主视图模型
│   ├── RecordingViewModel.swift   # 录音视图模型
│   └── StoryGenerationViewModel.swift # 故事生成视图模型
├── Views/
│   ├── HomeView.swift             # 首页
│   ├── VoiceView.swift            # 声音录制页面
│   ├── StoriesView.swift          # 故事页面
│   └── SettingsView.swift         # 设置页面
├── Services/
│   ├── AudioService.swift         # 音频服务
│   ├── VoiceCloningService.swift  # 声音克隆服务
│   ├── StoryGenerationService.swift # 故事生成服务
│   └── TextToSpeechService.swift  # 语音合成服务
└── Resources/
    └── Assets.xcassets            # 应用资源
```

## 安装与配置

### 前置要求
- Xcode 16.0+
- iOS 18.5+ 设备或模拟器
- MiniMax API密钥（必需）
- DeepSeek API密钥（可选）

### 构建步骤
1. 克隆项目到本地
```bash
git clone https://github.com/your-username/EchoTales.git
cd EchoTales
```

2. 使用Xcode打开项目
```bash
open EchoTales.xcodeproj
```

3. 配置API密钥
   - 启动应用后进入设置页面
   - 输入MiniMax API密钥（必需）
   - 输入DeepSeek API密钥（可选，用于故事生成）

4. 构建并运行
   - 选择目标设备或模拟器
   - 按Cmd+R运行项目

## 使用指南

### 1. 录制声音
- 进入"声音"标签页
- 选择录音语言
- 点击录音按钮开始15秒录音
- 录音完成后可以播放预览

### 2. 创建声音克隆
- 在录音完成后，输入声音克隆名称
- 点击"创建声音克隆"按钮
- 等待API处理完成

### 3. 生成故事
- 进入"故事"标签页
- 输入故事主题（如"小红帽"）
- 选择语言和声音克隆
- 点击"生成故事"按钮

### 4. 播放故事
- 在故事列表中选择故事
- 点击播放按钮听取AI朗读的故事

## API配置

### MiniMax API
1. 访问[MiniMax开放平台](https://www.minimaxi.com/)
2. 注册账号并获取API密钥
3. 在应用设置中输入API密钥

### DeepSeek API（可选）
1. 访问[DeepSeek平台](https://www.deepseek.com/)
2. 注册账号并获取API密钥
3. 在应用设置中输入API密钥

## 测试

运行单元测试：
```bash
xcodebuild test -project EchoTales.xcodeproj -scheme EchoTales -destination 'platform=iOS Simulator,name=iPhone 16'
```

## 许可证

本项目采用MIT许可证 - 查看[LICENSE](LICENSE)文件了解详情。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- GitHub Issues: [项目Issues页面]
- Email: <EMAIL>

---

**注意**：本应用需要麦克风权限来录制声音，请在首次使用时允许相关权限。
